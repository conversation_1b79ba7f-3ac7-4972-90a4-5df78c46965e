
# BlazePose Lite Console Log Output

## Test Results

This file contains the complete console log output from the BlazePose Lite implementation test.

Please paste the full console log results below:

---

```

index-BNB6hnok.js:22493 🚀 STARTING BLAZEPOSE FULL ANALYSIS:
index-BNB6hnok.js:22493 analysisMode: 3D
index-BNB6hnok.js:22493 activityType: Running
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 analysisQuality: Full
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:
index-BNB6hnok.js:22493 analysisMode: 3D type: string
index-BNB6hnok.js:22493 analysisType: running type: string
index-BNB6hnok.js:22493 viewType: side type: string
index-BNB6hnok.js:22493 videoSetup: Treadmill type: string
index-BNB6hnok.js:22493 modelQuality: Full type: string
index-BNB6hnok.js:22493 overlayStyle: Medical type: string
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 === BLAZEPOSE FULL MODEL ===
index-BNB6hnok.js:22493 ✅ 3D Running Analysis - BlazePose Full
index-BNB6hnok.js:22493 analysisType: running
index-BNB6hnok.js:22493 viewType: side
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 ============================
index-BNB6hnok.js:22493 ✅ Using Side View BlazePose Full
index-BNB6hnok.js:22493 🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:
index-BNB6hnok.js:22493 analysisMode: 3D type: string
index-BNB6hnok.js:22493 analysisType: running type: string
index-BNB6hnok.js:22493 viewType: rear type: string
index-BNB6hnok.js:22493 videoSetup: Treadmill type: string
index-BNB6hnok.js:22493 modelQuality: Full type: string
index-BNB6hnok.js:22493 overlayStyle: Medical type: string
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 === BLAZEPOSE FULL MODEL ===
index-BNB6hnok.js:22493 ✅ 3D Running Analysis - BlazePose Full
index-BNB6hnok.js:22493 analysisType: running
index-BNB6hnok.js:22493 viewType: rear
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 ============================
index-BNB6hnok.js:22493 🔄 PHASE 4 ENHANCED: Initializing BlazePose with coordinate fix...
index-BNB6hnok.js:22493 🎯 PHASE 4 ENHANCED: Model Quality: Full
index-BNB6hnok.js:22439 🔍 MODEL VERSION CHECK: Current BlazePose model versions:
index-BNB6hnok.js:22439 🔍 Detector: 1 verified: 2024-01-07
index-BNB6hnok.js:22439 🔍 Landmark Full: 1 verified: 2024-01-07
index-BNB6hnok.js:22439 🔍 Landmark Lite: 1 verified: 2024-01-07
index-BNB6hnok.js:22439 🔍 Landmark Heavy: 1 verified: 2024-01-07
index-BNB6hnok.js:22439 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark lite model - optimized for speed verification is older than 6 months - consider checking for updates
overrideMethod @ hook.js:608
(anonymous) @ index-BNB6hnok.js:22439
Pxe @ index-BNB6hnok.js:22439
(anonymous) @ index-BNB6hnok.js:22493
(anonymous) @ index-BNB6hnok.js:22493
Em @ index-BNB6hnok.js:40
Wl @ index-BNB6hnok.js:40
(anonymous) @ index-BNB6hnok.js:40
w @ index-BNB6hnok.js:25
D @ index-BNB6hnok.js:25
index-BNB6hnok.js:22439 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark full model - balanced accuracy/speed verification is older than 6 months - consider checking for updates
overrideMethod @ hook.js:608
(anonymous) @ index-BNB6hnok.js:22439
Pxe @ index-BNB6hnok.js:22439
(anonymous) @ index-BNB6hnok.js:22493
(anonymous) @ index-BNB6hnok.js:22493
Em @ index-BNB6hnok.js:40
Wl @ index-BNB6hnok.js:40
(anonymous) @ index-BNB6hnok.js:40
w @ index-BNB6hnok.js:25
D @ index-BNB6hnok.js:25
index-BNB6hnok.js:22439 ⚠️ MODEL VERSION CHECK: BlazePose 3D landmark heavy model - maximum accuracy verification is older than 6 months - consider checking for updates
overrideMethod @ hook.js:608
(anonymous) @ index-BNB6hnok.js:22439
Pxe @ index-BNB6hnok.js:22439
(anonymous) @ index-BNB6hnok.js:22493
(anonymous) @ index-BNB6hnok.js:22493
Em @ index-BNB6hnok.js:40
Wl @ index-BNB6hnok.js:40
(anonymous) @ index-BNB6hnok.js:40
w @ index-BNB6hnok.js:25
D @ index-BNB6hnok.js:25
index-BNB6hnok.js:22493 🔍 ENHANCED Side View BlazePose Custom Overlay initialized
index-BNB6hnok.js:22493 👤 User height: 5'10"
index-BNB6hnok.js:22493 ⏸️ Not ready for pose detection - initialized: false video: true canvas: true
index-BNB6hnok.js:22493 ✅ WebGL backend ready for Phase 4 BlazePose
index-BNB6hnok.js:22493 📊 TensorFlow.js Backend Info: {backend: 'webgl', memory: {…}, platform: aH}
index-BNB6hnok.js:22454 🔧 MODEL CONFIG: Validating BlazePose model configuration
index-BNB6hnok.js:22454 🔧 MODEL CONFIG: Input model type: full
index-BNB6hnok.js:22454 🔧 MODEL CONFIG: Default model type: full
index-BNB6hnok.js:22454 ✅ MODEL CONFIG: Final model type selected: full
index-BNB6hnok.js:22454 ✅ MODEL CONFIG: Final configuration: {modelType: 'full', enableSmoothing: true, enableSegmentation: false, detectorUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...', landmarkUrl: 'https://tfhub.dev/mediapipe/tfjs-model/blazepose_3...'}
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Creating anchors with config: {reduceBoxesInLowestLayer: false, interpolatedScaleAspectRatio: 1, featureMapHeight: Array(6), featureMapWidth: Array(6), numLayers: 6, …}
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Feature map dimensions: {heights: Array(6), widths: Array(6), numLayers: 6, aspectRatios: Array(2)}
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 0: 28x28
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 1: 16x16
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 2: 8x8
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 3: 4x4
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 4: 2x2
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Layer 5: 1x1
index-BNB6hnok.js:22227 🔧 SSD ANCHORS: Created 2250 anchors
index-BNB6hnok.js:22493 🔧 PHASE 5: Custom BlazePose detector loaded: Full
index-BNB6hnok.js:22493 ✅ PHASE 5: Custom BlazePose detector loaded successfully
index-BNB6hnok.js:22493 ✅ PHASE 5: Custom BlazePose initialization complete
index-BNB6hnok.js:22493 🔍 ENHANCED Side View BlazePose Custom Overlay initialized
index-BNB6hnok.js:22493 👤 User height: 5'10"
index-BNB6hnok.js:22493 ✅ Starting ENHANCED BlazePose Custom detection loop
index-BNB6hnok.js:22493 🚀 Starting ENHANCED pose detection loop
index-BNB6hnok.js:22493 🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:
index-BNB6hnok.js:22493 analysisMode: 3D type: string
index-BNB6hnok.js:22493 analysisType: running type: string
index-BNB6hnok.js:22493 viewType: rear type: string
index-BNB6hnok.js:22493 videoSetup: Treadmill type: string
index-BNB6hnok.js:22493 modelQuality: Full type: string
index-BNB6hnok.js:22493 overlayStyle: Medical type: string
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 === BLAZEPOSE FULL MODEL ===
index-BNB6hnok.js:22493 ✅ 3D Running Analysis - BlazePose Full
index-BNB6hnok.js:22493 analysisType: running
index-BNB6hnok.js:22493 viewType: rear
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 ============================
index-BNB6hnok.js:22493 🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:
index-BNB6hnok.js:22493 analysisMode: 3D type: string
index-BNB6hnok.js:22493 analysisType: running type: string
index-BNB6hnok.js:22493 viewType: side type: string
index-BNB6hnok.js:22493 videoSetup: Treadmill type: string
index-BNB6hnok.js:22493 modelQuality: Full type: string
index-BNB6hnok.js:22493 overlayStyle: Medical type: string
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 === BLAZEPOSE FULL MODEL ===
index-BNB6hnok.js:22493 ✅ 3D Running Analysis - BlazePose Full
index-BNB6hnok.js:22493 analysisType: running
index-BNB6hnok.js:22493 viewType: side
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 ============================
index-BNB6hnok.js:22493 ✅ Using Side View BlazePose Full
index-BNB6hnok.js:22493 🛑 Cleaning up ENHANCED Side View BlazePose detection
index-BNB6hnok.js:22493 🔍 ENHANCED Side View BlazePose Custom Overlay initialized
index-BNB6hnok.js:22493 👤 User height: 5'10"
index-BNB6hnok.js:22493 ✅ Starting ENHANCED BlazePose Custom detection loop
index-BNB6hnok.js:22493 🚀 Starting ENHANCED pose detection loop
index-BNB6hnok.js:22493 🎨 CANVAS POSITIONING: {canvasSize: '570x1014', videoRect: '570.6666870117188x1014.5', position: '0px, 0px', zIndex: '10'}
index-BNB6hnok.js:22493 🚨 PHASE 6F: FORCED CANVAS DEBUG - Drawing large indicators
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large red square at 0,0 size 100x100
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large blue square at top right
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large green square at bottom left
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large yellow square at bottom right
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large animated magenta center square, size: 67.6
index-BNB6hnok.js:22493 🚨 PHASE 6F: Drew large frame counter: 1
index-BNB6hnok.js:22493 🎯 ENHANCED FRAME 1: Starting pose detection
index-BNB6hnok.js:22493 🔍 PHASE 5: Running custom BlazePose with complete pipeline
index-BNB6hnok.js:22493 🚀 PHASE 5: Custom BlazePose with complete pipeline...
index-BNB6hnok.js:22493 📐 PHASE 5: Video dimensions: {width: 1080, height: 1920}
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Converting image to tensor
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Original size: {width: 1080, height: 1920}
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Created tensor with shape: (3) [1920, 1080, 3]
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Resizing to target size: {width: 224, height: 224}
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Aspect ratio preserving resize: {newWidth: 126, newHeight: 224}
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Applied padding: {top: 0, bottom: 0, left: 49, right: 49}
index-BNB6hnok.js:22219 🔧 IMAGE CONVERSION: Final tensor shape: (3) [224, 224, 3]
index-BNB6hnok.js:22469 🔧 DETECTOR MODEL: Input tensor shape: (4) [1, 224, 224, 3]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Processing detector output tensors
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Input tensors count: 1
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Processing started at: 2025-07-07T22:37:04.256Z
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Main tensor shape: (3) [1, 2254, 13]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Tensor dimensions - batch: 1 anchors: 2254 values: 13
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Starting BlazePose tensor format verification
index-BNB6hnok.js:22235 ✅ DETECTOR RESULT: BlazePose tensor format validation passed
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Confirmed format: [batch=1, anchors=2254, values=13]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Value structure: [4 box coords] + [1 score] + [8 additional values]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Processing BlazePose single tensor format
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Starting tensor slicing verification
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: BlazePose coordinate order: [yCenter, xCenter, height, width] at indices [0,1,2,3]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: BlazePose score at index [4]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Box slice parameters: {start: Array(3), size: Array(3)}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Score slice parameters: {start: Array(3), size: Array(3)}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Executing tensor slicing...
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Starting comprehensive tensor extraction testing
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Testing tensor accessibility...
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Tensor metadata validated: {dtype: 'float32', size: 29302, rank: 3, memory: '114.46 KB'}
index-BNB6hnok.js:22235 ✅ DETECTOR RESULT: Tensor integrity tests passed
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Index validation - first detection coordinates: (4) ['-130.8864', '-6.0170', '30.7018', '29.8205']
index-BNB6hnok.js:22235 ✅ DETECTOR RESULT: Tensor indexing validation passed
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Slicing completed successfully
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Boxes tensor shape (3D): (3) [1, 2254, 4]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Scores tensor shape (3D): (3) [1, 2254, 1]
index-BNB6hnok.js:22235 ✅ DETECTOR RESULT: Tensor slicing validation passed
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Starting coordinate transformation validation
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Sample coordinate data for validation:
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Detection 0 coords: {yCenter: '-130.8864', xCenter: '-6.0170', height: '30.7018', width: '29.8205'}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Unusual coordinate values at detection 0 - may affect anchor transformation
overrideMethod @ hook.js:608
gxe @ index-BNB6hnok.js:22235
detectPose @ index-BNB6hnok.js:22469
estimatePoses @ index-BNB6hnok.js:22469
d @ index-BNB6hnok.js:22493
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Detection 1 coords: {yCenter: '-53.6333', xCenter: '-17.9270', height: '28.5821', width: '44.4549'}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Detection 2 coords: {yCenter: '-319.0079', xCenter: '-11.6418', height: '61.3013', width: '-21.8397'}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Unusual coordinate values at detection 2 - may affect anchor transformation
overrideMethod @ hook.js:608
gxe @ index-BNB6hnok.js:22235
detectPose @ index-BNB6hnok.js:22469
estimatePoses @ index-BNB6hnok.js:22469
d @ index-BNB6hnok.js:22493
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Coordinate format verification:
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ yCenter at index [0] - relative Y coordinate for anchor transformation
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ xCenter at index [1] - relative X coordinate for anchor transformation
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ height at index [2] - relative height for anchor scaling
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ width at index [3] - relative width for anchor scaling
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ Coordinate order matches BlazePose anchor transformation expectations
index-BNB6hnok.js:22235 ✅ DETECTOR RESULT: Coordinate transformation validation completed
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Final tensor validation for anchor transformation:
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Boxes tensor format: [numDetections=2254, coordinates=4]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Logits tensor format: [numDetections=2254, scores=1]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ Tensor shapes compatible with anchor-based coordinate transformation
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: ✓ Ready for downstream tensors_to_detections processing
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Reshaped boxes to 2D: (2) [2254, 4]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Reshaped logits to 2D: (2) [2254, 1]
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Coordinate interpretation: boxes2D[:, 0]=yCenter, [:, 1]=xCenter, [:, 2]=height, [:, 3]=width
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Score interpretation: logits2D[:, 0]=confidence_score
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Pipeline integration - tensors ready for downstream processing
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Processing completed successfully
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Performance metrics: {totalTime: '21.30ms', tensorsProcessed: 1, detectionsExtracted: 2254, avgTimePerDetection: '0.009ms'}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Data quality analysis: {boxStats: {…}, scoreStats: {…}}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Memory usage summary: {inputTensor: '114.46 KB', outputBoxes: '35.22 KB', outputLogits: '8.80 KB', totalOutput: '44.02 KB'}
index-BNB6hnok.js:22235 🔧 DETECTOR RESULT: Processing summary: {status: 'SUCCESS', format: 'BlazePose single tensor', inputShape: Array(3), outputShapes: {…}, processingTime: '21.30ms', …}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Converting tensor to detections
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Tensor shape: (3) [1, 2254, 13]
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Anchors: 2250
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Config numCoords: 13
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Tensor dimensions - batch: 1 boxes: 2254 coords: 13
index-BNB6hnok.js:22370 ✅ TENSORS TO DETECTIONS: BlazePose tensor shape validation passed
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Starting comprehensive tensor data extraction
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Tensor metadata: {shape: Array(3), dtype: 'float32', size: 29302, rank: 3, memory: '114.46 KB'}
index-BNB6hnok.js:22493 [Violation] 'requestAnimationFrame' handler took 579ms
index-BNB6hnok.js:22493 🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:
index-BNB6hnok.js:22493 analysisMode: 3D type: string
index-BNB6hnok.js:22493 analysisType: running type: string
index-BNB6hnok.js:22493 viewType: side type: string
index-BNB6hnok.js:22493 videoSetup: Treadmill type: string
index-BNB6hnok.js:22493 modelQuality: Full type: string
index-BNB6hnok.js:22493 overlayStyle: Medical type: string
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 === BLAZEPOSE FULL MODEL ===
index-BNB6hnok.js:22493 ✅ 3D Running Analysis - BlazePose Full
index-BNB6hnok.js:22493 analysisType: running
index-BNB6hnok.js:22493 viewType: side
index-BNB6hnok.js:22493 videoSetup: Treadmill
index-BNB6hnok.js:22493 userHeight: {feet: 5, inches: 10}
index-BNB6hnok.js:22493 ============================
index-BNB6hnok.js:22493 ✅ Using Side View BlazePose Full
index-BNB6hnok.js:22493 🛑 Cleaning up ENHANCED Side View BlazePose detection
index-BNB6hnok.js:22493 🔍 ENHANCED Side View BlazePose Custom Overlay initialized
index-BNB6hnok.js:22493 👤 User height: 5'10"
index-BNB6hnok.js:22493 ✅ Starting ENHANCED BlazePose Custom detection loop
index-BNB6hnok.js:22493 🛑 Cleaning up ENHANCED Side View BlazePose detection
index-BNB6hnok.js:22493 🔍 ENHANCED Side View BlazePose Custom Overlay initialized
index-BNB6hnok.js:22493 👤 User height: 5'10"
index-BNB6hnok.js:22493 ✅ Starting ENHANCED BlazePose Custom detection loop
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Tensor data extraction completed in 11.60ms
index-BNB6hnok.js:22370 ✅ TENSORS TO DETECTIONS: Tensor data extracted successfully - 29302 values
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Tensor data statistics: {min: -587573.6875, max: 1162.1529541015625, mean: '-964.6382', nonZeroCount: 29302, infiniteCount: 0, …}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Processing 2250 detections
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Anchor statistics: {total: 2250, validAnchors: 2250, avgWidth: '0.2377', avgHeight: '0.1681', minWidth: 0.1484375, …}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Sample tensor data for first detection: {indices: '[0:12]', values: Array(13)}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Detection 0 raw values: {baseIndex: 0, yCenter: '-130.8864', xCenter: '-6.0170', height: '30.7018', width: '29.8205', …}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 0: Raw values {yCenter: -130.8863983154297, xCenter: -6.017020225524902, h: 30.701818466186523, w: 29.820510864257812, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 0: Transformed {anchorCenterX: -0.042313059398106166, anchorCenterY: -1.2910068402971542, boxWidth: 0.22132410407066347, boxHeight: 0.2278650589287281}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 0: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Detection 1 raw values: {baseIndex: 13, yCenter: '-53.6333', xCenter: '-17.9270', height: '28.5821', width: '44.4549', …}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 1: Raw values {yCenter: -53.6333122253418, xCenter: -17.927043914794922, h: 28.582067489624023, w: 44.45489501953125, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 1: Transformed {anchorCenterX: -0.16141329629080636, anchorCenterY: -0.5184759793962751, boxWidth: 0.46660374748412964, boxHeight: 0.15000035199322242}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 1: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TENSORS TO DETECTIONS: Detection 2 raw values: {baseIndex: 26, yCenter: '-319.0079', xCenter: '-11.6418', height: '61.3013', width: '-21.8397', …}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 3: Raw values {yCenter: -68.58247375488281, xCenter: -7.925967216491699, h: 15.95534896850586, w: 42.17305374145508, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 3: Transformed {anchorCenterX: -0.02568824359348843, anchorCenterY: -0.6679675946916852, boxWidth: 0.44265327608955013, boxHeight: 0.08373459905653802}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 3: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 5: Raw values {yCenter: -74.9696273803711, xCenter: -19.460796356201172, h: 3.555122137069702, w: 43.95259094238281, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 5: Transformed {anchorCenterX: -0.10532224927629742, anchorCenterY: -0.731839130946568, boxWidth: 0.4613315054808352, boxHeight: 0.018657487675898258}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 5: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 7: Raw values {yCenter: -72.21315002441406, xCenter: -16.97638702392578, h: 4.567409038543701, w: 45.22773361206055, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 7: Transformed {anchorCenterX: -0.04476387023925782, anchorCenterY: -0.7042743573869977, boxWidth: 0.474715551219491, boxHeight: 0.023970028190833055}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 7: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 9: Raw values {yCenter: -66.5628890991211, xCenter: -12.229162216186523, h: 5.812053203582764, w: 43.92882537841797, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 9: Transformed {anchorCenterX: 0.038422663552420494, anchorCenterY: -0.647771748134068, boxWidth: 0.46108205935792346, boxHeight: 0.030501993134584772}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 9: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 11: Raw values {yCenter: -59.10848617553711, xCenter: -4.46121072769165, h: 10.809884071350098, w: 43.98545837402344, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 11: Transformed {anchorCenterX: 0.15181646415165492, anchorCenterY: -0.5732277188982282, boxWidth: 0.46167648586526655, boxHeight: 0.05673090011060542}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 11: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 13: Raw values {yCenter: -48.6111946105957, xCenter: -0.3003855347633362, h: 15.26706600189209, w: 44.22187805175781, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 13: Transformed {anchorCenterX: 0.22913900179522378, anchorCenterY: -0.4682548032488142, boxWidth: 0.4641579742944122, boxHeight: 0.080122450029863}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 13: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 15: Raw values {yCenter: -37.17267990112305, xCenter: 0.5882119536399841, h: 17.063682556152344, w: 44.49143981933594, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 15: Transformed {anchorCenterX: 0.2737392623935427, anchorCenterY: -0.3538696561540876, boxWidth: 0.46698732595242726, boxHeight: 0.08955119816481583}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 15: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 17: Raw values {yCenter: -25.64243507385254, xCenter: 1.120922565460205, h: 12.669124603271484, w: 44.71135330200195, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 17: Transformed {anchorCenterX: 0.3147806542260306, anchorCenterY: -0.23856720788138253, boxWidth: 0.4692955634387415, boxHeight: 0.06648830252138331}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 17: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 19: Raw values {yCenter: -14.133295059204102, xCenter: 1.008016586303711, h: 7.901196002960205, w: 44.82503128051758, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 19: Transformed {anchorCenterX: 0.3493658801487514, anchorCenterY: -0.12347580773489816, boxWidth: 0.47048874072008495, boxHeight: 0.041465936011862076}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 19: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 20: Raw values {yCenter: -53.67310333251953, xCenter: 13.325920104980469, h: 25.628602981567383, w: 3.56855845451355, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 20: Transformed {anchorCenterX: 0.5082592010498047, anchorCenterY: -0.5188738904680524, boxWidth: 0.026485394779592754, boxHeight: 0.19021228775382043}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 20: {score: '0.103', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 21: Raw values {yCenter: -8.68152904510498, xCenter: 0.848914384841919, h: 3.6706159114837646, w: 44.81691360473633, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 21: Transformed {anchorCenterX: 0.3834891438484192, anchorCenterY: -0.06895814759390695, boxWidth: 0.4704035366511353, boxHeight: 0.01926360571901828}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 21: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 22: Raw values {yCenter: -40.3254508972168, xCenter: 13.346884727478027, h: 12.782634735107422, w: 6.7972331047058105, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 22: Transformed {anchorCenterX: 0.544183132989066, anchorCenterY: -0.3853973661150251, boxWidth: 0.05044821444898844, boxHeight: 0.0948711171746254}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 22: {score: '0.880', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 23: Raw values {yCenter: -5.334842205047607, xCenter: 0.5189287662506104, h: 0.5050904154777527, w: 44.805301666259766, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 23: Transformed {anchorCenterX: 0.4159035733767918, anchorCenterY: -0.03549127919333322, boxWidth: 0.4702816563946115, boxHeight: 0.0026507438672016964}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 23: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 24: Raw values {yCenter: -28.360267639160156, xCenter: 8.939253807067871, h: 7.312674522399902, w: 8.69271183013916, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 24: Transformed {anchorCenterX: 0.5358211094992502, anchorCenterY: -0.26574553353445873, boxWidth: 0.06451622061431408, boxHeight: 0.054273756220936775}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 24: {score: '0.990', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 25: Raw values {yCenter: -3.7116405963897705, xCenter: 0.04630420356988907, h: 0.8078531622886658, w: 44.78873062133789, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 25: Transformed {anchorCenterX: 0.44689161346427037, anchorCenterY: -0.019259263106754847, boxWidth: 0.47010772478017676, boxHeight: 0.004239660365581612}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 25: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 26: Raw values {yCenter: -22.629005432128906, xCenter: 5.405219554901123, h: 6.666050910949707, w: 7.218292713165283, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 26: Transformed {anchorCenterX: 0.5361950526918684, anchorCenterY: -0.20843291146414622, boxWidth: 0.05357326623052359, boxHeight: 0.04947459660470486}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 26: {score: '0.992', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 27: Raw values {yCenter: -2.867305278778076, xCenter: 0.48122674226760864, h: 2.5552632808685303, w: 44.78382873535156, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 27: Transformed {anchorCenterX: 0.48695512456553325, anchorCenterY: -0.010815909930637906, boxWidth: 0.47005627401485706, boxHeight: 0.01341017026514193}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 27: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 28: Raw values {yCenter: -22.23670196533203, xCenter: 5.129299163818359, h: 7.49318265914917, w: 6.171350955963135, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 28: Transformed {anchorCenterX: 0.5691501344953265, anchorCenterY: -0.20450987679617746, boxWidth: 0.045802995376288895, boxHeight: 0.05561346504837275}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 28: {score: '0.981', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 29: Raw values {yCenter: -2.9376821517944336, xCenter: 0.7796566486358643, h: 1.9181928634643555, w: 44.7845573425293, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 29: Transformed {anchorCenterX: 0.5256537093435015, anchorCenterY: -0.011519678660801481, boxWidth: 0.4700639215605194, boxHeight: 0.010066787674299395}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 29: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 30: Raw values {yCenter: -24.168725967407227, xCenter: 4.988952159881592, h: 7.3814005851745605, w: 4.751068592071533, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 30: Transformed {anchorCenterX: 0.6034609501702445, anchorCenterY: -0.2238301168169294, boxWidth: 0.03526183720678091, boxHeight: 0.054783832468092444}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 30: {score: '0.938', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 31: Raw values {yCenter: -3.3542158603668213, xCenter: 1.2992565631866455, h: 1.334051489830017, w: 44.786231994628906, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 31: Transformed {anchorCenterX: 0.5665639942032951, anchorCenterY: -0.01568501574652536, boxWidth: 0.4700814989036909, boxHeight: 0.007001179782541254}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 31: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 32: Raw values {yCenter: -25.621864318847656, xCenter: 4.026363849639893, h: 6.794157981872559, w: 2.983246088027954, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 32: Transformed {anchorCenterX: 0.6295493527821132, anchorCenterY: -0.23836150033133374, boxWidth: 0.022141279559582474, boxHeight: 0.0504253912717104}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 32: {score: '0.927', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 33: Raw values {yCenter: -3.3688697814941406, xCenter: 1.9185700416564941, h: 1.7596116065979004, w: 44.785606384277344, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 33: Transformed {anchorCenterX: 0.6084714147022793, anchorCenterY: -0.015831554957798553, boxWidth: 0.4700749324246929, boxHeight: 0.009234544018093236}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 33: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 34: Raw values {yCenter: -25.7432861328125, xCenter: 3.1609959602355957, h: 6.581356048583984, w: 1.2786939144134521, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 34: Transformed {anchorCenterX: 0.6566099596023559, anchorCenterY: -0.23957571847098214, boxWidth: 0.00949030639603734, boxHeight: 0.04884600192308426}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 34: {score: '0.946', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 35: Raw values {yCenter: -3.531795024871826, xCenter: 3.0579700469970703, h: 1.95694899559021, w: 44.78559494018555, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 35: Transformed {anchorCenterX: 0.6555797004699707, anchorCenterY: -0.017460807391575406, boxWidth: 0.47007481230617465, boxHeight: 0.010270182109040144}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 35: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 36: Raw values {yCenter: -27.653257369995117, xCenter: 2.1665027141571045, h: 7.78542947769165, w: -0.057451196014881134, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 36: Transformed {anchorCenterX: 0.6823793128558567, anchorCenterY: -0.2586754308428083, boxWidth: 0.001, boxHeight: 0.05778248440474272}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 36: {score: '0.963', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 37: Raw values {yCenter: -3.8020071983337402, xCenter: 3.8475210666656494, h: 3.3923280239105225, w: 44.77745056152344, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 37: Transformed {anchorCenterX: 0.6991894963809422, anchorCenterY: -0.020162929126194544, boxWidth: 0.46998932796068243, boxHeight: 0.017803134704925595}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 37: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 38: Raw values {yCenter: -25.908039093017578, xCenter: 1.289746880531311, h: 7.686654090881348, w: 0.034671757370233536, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 38: Transformed {anchorCenterX: 0.7093260402338845, anchorCenterY: -0.24122324807303291, boxWidth: 0.001, boxHeight: 0.057049385830760004}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 38: {score: '0.989', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 39: Raw values {yCenter: -3.508718252182007, xCenter: 3.6445202827453613, h: 5.611574649810791, w: 44.77521896362305, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 39: Transformed {anchorCenterX: 0.732873774256025, anchorCenterY: -0.01723003966467721, boxWidth: 0.4699659048496225, boxHeight: 0.029449870028241787}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 39: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 40: Raw values {yCenter: -26.72894859313965, xCenter: 0.6610450744628906, h: 7.074586391448975, w: 1.867469310760498, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 40: Transformed {anchorCenterX: 0.738753307887486, anchorCenterY: -0.24943234307425366, boxWidth: 0.013860123790800573, boxHeight: 0.052506695874035364}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 40: {score: '0.996', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 41: Raw values {yCenter: -3.3065316677093506, xCenter: 1.8876796960830688, h: 7.474207878112793, w: 44.77507781982422, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 41: Transformed {anchorCenterX: 0.7510196541036878, anchorCenterY: -0.015208173819950647, boxWidth: 0.46996442338789735, boxHeight: 0.03922507750688202}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 41: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 42: Raw values {yCenter: -32.70494842529297, xCenter: -0.556250274181366, h: 4.965114593505859, w: 4.653077602386475, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 42: Transformed {anchorCenterX: 0.7622946401153292, anchorCenterY: -0.30919234139578683, boxWidth: 0.03453456033021212, boxHeight: 0.0368504598736763}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 42: {score: '0.996', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 43: Raw values {yCenter: -4.739598751068115, xCenter: 1.0932331085205078, h: 8.124191284179688, w: 44.76723098754883, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 43: Transformed {anchorCenterX: 0.778789473942348, anchorCenterY: -0.029538844653538295, boxWidth: 0.4698820621238798, boxHeight: 0.042636228213008574}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 43: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 44: Raw values {yCenter: -37.74486541748047, xCenter: -1.368574619293213, h: 3.9514970779418945, w: 5.921844959259033, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 44: Transformed {anchorCenterX: 0.7898856823784964, anchorCenterY: -0.35959151131766187, boxWidth: 0.04395119305700064, boxHeight: 0.02932751737535}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 44: {score: '0.997', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 45: Raw values {yCenter: -6.304372787475586, xCenter: 0.21890246868133545, h: 9.519928932189941, w: 44.7634391784668, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 45: Transformed {anchorCenterX: 0.805760453258242, anchorCenterY: -0.045186585017613, boxWidth: 0.46984226285483094, boxHeight: 0.049961140540212806}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 45: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 46: Raw values {yCenter: -50.02103042602539, xCenter: -0.9752601981163025, h: 5.753990650177002, w: 7.823097229003906, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 46: Transformed {anchorCenterX: 0.8295331123045513, anchorCenterY: -0.4823531614031111, boxWidth: 0.05806204974651337, boxHeight: 0.04270539935678244}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 46: {score: '0.992', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 47: Raw values {yCenter: -8.573513984680176, xCenter: -1.57980215549469, h: 11.9451904296875, w: 44.72665023803711, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 47: Transformed {anchorCenterX: 0.8234876927307674, anchorCenterY: -0.0678779969896589, boxWidth: 0.4694561218581438, boxHeight: 0.06268905388770973}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 47: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 48: Raw values {yCenter: -73.72294616699219, xCenter: 4.52579927444458, h: 9.729363441467285, w: 5.979318141937256, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 48: Transformed {anchorCenterX: 0.9202579927444458, anchorCenterY: -0.719372318812779, boxWidth: 0.044377751834690574, boxHeight: 0.07221011929214001}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 48: {score: '0.726', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 49: Raw values {yCenter: -12.318070411682129, xCenter: -3.275573253631592, h: 13.014952659606934, w: 44.67833709716797, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 49: Transformed {anchorCenterX: 0.8422442674636841, anchorCenterY: -0.10532356125967843, boxWidth: 0.4689490215135728, boxHeight: 0.06830322826803481}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 49: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 51: Raw values {yCenter: -17.771129608154297, xCenter: -3.1238322257995605, h: 11.846378326416016, w: 44.647193908691406, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 51: Transformed {anchorCenterX: 0.8794759634562901, anchorCenterY: -0.15985415322440014, boxWidth: 0.4686221389858921, boxHeight: 0.06217048222464533}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 51: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 53: Raw values {yCenter: -23.592832565307617, xCenter: -4.1990966796875, h: 12.428173065185547, w: 44.640235900878906, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 53: Transformed {anchorCenterX: 0.9044376046316964, anchorCenterY: -0.21807118279593332, boxWidth: 0.4685491069267924, boxHeight: 0.06522377484019581}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 53: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 54: Raw values {yCenter: -116.07026672363281, xCenter: 4.892804145812988, h: 15.257164001464844, w: 4.789554595947266, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 54: Transformed {anchorCenterX: 1.0310708986009869, anchorCenterY: -1.1428455243791853, boxWidth: 0.035547475516796115, boxHeight: 0.1132367640733719}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 54: {score: '0.126', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 55: Raw values {yCenter: -28.536104202270508, xCenter: -1.0407196283340454, h: 7.561893463134766, w: 44.70098876953125, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 55: Transformed {anchorCenterX: 0.9717356608595167, anchorCenterY: -0.2675038991655622, boxWidth: 0.46918677610070764, boxHeight: 0.0396852565552592}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 55: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 56: Raw values {yCenter: -182.45823669433594, xCenter: -10.803071975708008, h: 6.158829689025879, w: 16.667171478271484, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 56: Transformed {anchorCenterX: -0.09017357689993721, anchorCenterY: -1.7710109383719308, boxWidth: 0.12370166331529618, boxHeight: 0.045710064098238946}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 56: {score: '0.928', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 57: Raw values {yCenter: -71.57030487060547, xCenter: -23.626632690429688, h: 22.435813903808594, w: 44.74247741699219, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 57: Transformed {anchorCenterX: -0.21840918404715404, anchorCenterY: -0.6621316201346261, boxWidth: 0.46962224576889194, boxHeight: 0.11774445582172938}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 57: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 59: Raw values {yCenter: -115.02847290039062, xCenter: -25.84639549255371, h: -7.864802360534668, w: 41.821571350097656, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 59: Transformed {anchorCenterX: -0.2048925263541085, anchorCenterY: -1.0967133004324776, boxWidth: 0.4389640760383521, boxHeight: 0.04127494006042756}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 59: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 60: Raw values {yCenter: -665.6317138671875, xCenter: -24.64324951171875, h: 38.28273391723633, w: 13.537749290466309, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 60: Transformed {anchorCenterX: -0.1571467808314732, anchorCenterY: -6.6027457101004465, boxWidth: 0.10047548301517964, boxHeight: 0.2841296657919884}
index-BNB6hnok.js:22370 🔧 REJECT DETECTION 60: Invalid bounds {anchorCenterX: '-0.157147', anchorCenterY: '-6.602746', boxWidth: '0.100475', boxHeight: '0.284130'}
overrideMethod @ hook.js:608
Ixe @ index-BNB6hnok.js:22370
await in Ixe
detectPose @ index-BNB6hnok.js:22469
estimatePoses @ index-BNB6hnok.js:22469
d @ index-BNB6hnok.js:22493
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 61: Raw values {yCenter: -127.24359130859375, xCenter: -33.1819953918457, h: -6.142132759094238, w: 41.937896728515625, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 61: Transformed {anchorCenterX: -0.24253423963274273, anchorCenterY: -1.218864484514509, boxWidth: 0.4401850407369188, boxHeight: 0.032234269833268714}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 61: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 62: Raw values {yCenter: -639.5175170898438, xCenter: -30.918790817260742, h: 40.25885772705078, w: 3.035581588745117, anchor: {…}}anchor: {xCenter: 0.125, yCenter: 0.05357142857142857, width: 0.1484375, height: 0.1484375}h: 40.25885772705078w: 3.035581588745117xCenter: -30.918790817260742yCenter: -639.5175170898438[[Prototype]]: Object
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 62: Transformed {anchorCenterX: -0.18418790817260744, anchorCenterY: -6.341603742327009, boxWidth: 0.022529707103967667, boxHeight: 0.298796209692955}anchorCenterX: -0.18418790817260744anchorCenterY: -6.341603742327009boxHeight: 0.298796209692955boxWidth: 0.022529707103967667[[Prototype]]: Object
index-BNB6hnok.js:22370 🔧 REJECT DETECTION 62: Invalid bounds {anchorCenterX: '-0.184188', anchorCenterY: '-6.341604', boxWidth: '0.022530', boxHeight: '0.298796'}anchorCenterX: "-0.184188"anchorCenterY: "-6.341604"boxHeight: "0.298796"boxWidth: "0.022530"[[Prototype]]: Object
overrideMethod @ hook.js:608
Ixe @ index-BNB6hnok.js:22370
await in Ixe
detectPose @ index-BNB6hnok.js:22469
estimatePoses @ index-BNB6hnok.js:22469
d @ index-BNB6hnok.js:22493
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 63: Raw values {yCenter: -124.11800384521484, xCenter: -25.862930297851562, h: -0.2247902899980545, w: 42.430328369140625, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 63: Transformed {anchorCenterX: -0.1336293029785156, anchorCenterY: -1.18760860988072, boxWidth: 0.44535366049846437, boxHeight: 0.001179712511581165}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 63: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 64: Raw values {yCenter: -520.2691650390625, xCenter: -28.110820770263672, h: 39.51290512084961, w: 0.9636252522468567, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 64: Transformed {anchorCenterX: -0.120393921988351, anchorCenterY: -5.149120221819197, boxWidth: 0.00715190616901964, boxHeight: 0.29325984269380573}
index-BNB6hnok.js:22370 🔧 REJECT DETECTION 64: Invalid bounds {anchorCenterX: '-0.120394', anchorCenterY: '-5.149120', boxWidth: '0.007152', boxHeight: '0.293260'}
overrideMethod @ hook.js:608
Ixe @ index-BNB6hnok.js:22370
await in Ixe
detectPose @ index-BNB6hnok.js:22469
estimatePoses @ index-BNB6hnok.js:22469
d @ index-BNB6hnok.js:22493
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
requestAnimationFrame
k @ index-BNB6hnok.js:22493
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 65: Raw values {yCenter: -112.47525787353516, xCenter: -17.395462036132812, h: 3.0896499156951904, w: 44.374534606933594, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 65: Transformed {anchorCenterX: -0.013240334647042412, anchorCenterY: -1.071181150163923, boxWidth: 0.4657602752489356, boxHeight: 0.016214662394816313}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 65: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 66: Raw values {yCenter: -407.95263671875, xCenter: -28.14137840270996, h: 40.50830841064453, w: 3.2328269481658936, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 66: Transformed {anchorCenterX: -0.08498521259852818, anchorCenterY: -4.025954938616072, boxWidth: 0.023993637505918743, boxHeight: 0.3006476014852524}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 66: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 67: Raw values {yCenter: -96.37039184570312, xCenter: -8.642682075500488, h: 8.209193229675293, w: 44.53849792480469, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 67: Transformed {anchorCenterX: 0.11000175067356655, anchorCenterY: -0.9101324898856027, boxWidth: 0.46748125329949614, boxHeight: 0.04308232336512023}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 67: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 68: Raw values {yCenter: -293.8636779785156, xCenter: -21.00802993774414, h: 40.412296295166016, w: 5.97944450378418, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 68: Transformed {anchorCenterX: 0.02206255776541574, anchorCenterY: -2.885065351213728, boxWidth: 0.04437868967652321, boxHeight: 0.2999350115656853}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 68: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 69: Raw values {yCenter: -76.8655014038086, xCenter: -3.0818164348602295, h: 14.589038848876953, w: 44.30393981933594, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 69: Transformed {anchorCenterX: 0.20132469279425486, anchorCenterY: -0.7150835854666574, boxWidth: 0.4650193041493202, boxHeight: 0.07656412410924322}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 69: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 70: Raw values {yCenter: -195.74114990234375, xCenter: -11.531384468078613, h: 35.013824462890625, w: 8.05315113067627, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 70: Transformed {anchorCenterX: 0.1525432981763567, anchorCenterY: -1.9038400704520089, boxWidth: 0.05976948104798794, boxHeight: 0.2598682284355164}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 70: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 71: Raw values {yCenter: -55.97190475463867, xCenter: -1.7654281854629517, h: 14.91463851928711, w: 44.54218292236328, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 71: Transformed {anchorCenterX: 0.2502028610025133, anchorCenterY: -0.5061476189749581, boxWidth: 0.4675199314623746, boxHeight: 0.07827289011044768}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 71: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 72: Raw values {yCenter: -146.89369201660156, xCenter: 1.7397440671920776, h: 32.62236404418945, w: 8.1066255569458, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 72: Transformed {anchorCenterX: 0.32096886924334933, anchorCenterY: -1.415365491594587, boxWidth: 0.06016636155545712, boxHeight: 0.2421191081404686}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 72: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 73: Raw values {yCenter: -37.201416015625, xCenter: -2.1056079864501953, h: 11.158447265625, w: 44.82176208496094, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 73: Transformed {anchorCenterX: 0.2825153487069266, anchorCenterY: -0.31844273158482145, boxWidth: 0.4704544268633698, boxHeight: 0.058560180020188514}
index-BNB6hnok.js:22370 🔧 CREATED DETECTION 73: {score: '1.000', bbox: {…}, relBbox: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 74: Raw values {yCenter: -116.27371215820312, xCenter: 12.582403182983398, h: 27.56416130065918, w: 9.410547256469727, anchor: {…}}
index-BNB6hnok.js:22370 🔧 TRANSFORM DEBUG 74: Transformed {anchorCenterX: 0.4651097461155483, anchorCenterY: -1.1091656930106026, boxWidth: 0.06984390541911126, boxHeight: 0.20457775965332986}




```

---

## Notes

- BlazePose Lite should show successful initialization
- Look for 33 keypoints detection (not 17 like MoveNet)
- 3D coordinates (x, y, z) should be captured
- Error states should be properly handled

## Analysis Focus

- Initialization success/failure
- Pose detection accuracy
- 3D coordinate capture
- Performance metrics
- Any error messages or warnings
