
import { useState, useEffect } from 'react';
import * as tf from '@tensorflow/tfjs';
import { load as loadBlazePose } from '@/blazepose_tfjs/detector';
import { PoseDetector } from '@/pose_detector';
import { normalizedKeypointsToKeypoints } from '@/shared/calculators/normalized_keypoints_to_keypoints';
import { calculateLandmarkProjection } from '@/shared/calculators/calculate_landmark_projection';
import { calculateWorldLandmarkProjection } from '@/shared/calculators/calculate_world_landmark_projection';
import { getImageSize } from '@/shared/calculators/image_utils';
import { convertImageToTensor } from '@/shared/calculators/convert_image_to_tensor';
import { calculateROIFromLandmarks, expandROI, ROISmoothingFilter, validateROI } from '@/shared/calculators/roi_processing';
import { safeTensorDispose, validateTensor, filterNaNValues } from '@/shared/calculators/tensor_utils';
import { validateAndCleanPoseCoordinates } from '@/shared/calculators/blazepose_tensor_processor';
import { KeypointsSmoothingFilter } from '@/shared/filters/keypoints_smoothing';
import { LowPassVisibilityFilter } from '@/shared/filters/visibility_smoothing';
import { PoseStabilityFilter } from '@/shared/filters/pose_stability_filter';
import { BLAZEPOSE_KEYPOINTS } from '@/shared/calculators/blazepose_constants';
import { BLAZEPOSE_MODEL_VERSIONS, checkModelVersions } from '@/blazepose_tfjs/constants'; // TASK 6: Import versioned URLs and version checker

export const useBlazePoseDetection = (modelQuality: 'Full' | 'Heavy' = 'Full') => {
  const [detector, setDetector] = useState<PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  
  // PHASE 4: Enhanced Smoothing and Filtering Pipeline
  const [keypointsFilter] = useState(() => new KeypointsSmoothingFilter({
    frequency: 30,
    minCutOff: 1,
    beta: 0.007,
    derivateCutOff: 1,
    thresholdCutOff: 0.001,
    thresholdBeta: 0.1,
    disableValueScaling: true,
    velocitySmoothing: 0.15,
    accelerationDamping: 0.1
  }));
  
  const [visibilityFilter] = useState(() => new LowPassVisibilityFilter(0.1, 0.3, true));
  const [roiFilter] = useState(() => new ROISmoothingFilter(0.2));
  const [stabilityFilter] = useState(() => new PoseStabilityFilter({
    stabilityThreshold: 0.75,
    minStableFrames: 3,
    maxInstabilityFrames: 8,
    positionTolerance: 40.0
  }));

  useEffect(() => {
    const initBlazePose = async () => {
      console.log('🔄 PHASE 4 ENHANCED: Initializing BlazePose with coordinate fix...');
      console.log('🎯 PHASE 4 ENHANCED: Model Quality:', modelQuality);
      
      // TASK 6: Check model versions at startup
      checkModelVersions();
      
      try {
        await tf.ready();
        
        try {
          await tf.setBackend('webgl');
          await tf.ready();
          console.log('✅ WebGL backend ready for Phase 4 BlazePose');
        } catch (webglError) {
          console.warn('⚠️ WebGL failed, falling back to CPU:', webglError);
          await tf.setBackend('cpu');
          await tf.ready();
          console.log('✅ CPU backend ready for Phase 4 BlazePose');
        }

        console.log('📊 TensorFlow.js Backend Info:', {
          backend: tf.getBackend(),
          memory: tf.memory(),
          platform: tf.env().platform
        });
        
        // PHASE 5: Use our custom BlazePose detector implementation
        // TASK 1: Full model optimization with enhanced settings
        // TASK 6: Use versioned model URLs with validation
        const blazePoseDetector = await loadBlazePose({
          runtime: 'tfjs' as const,
          modelType: modelQuality.toLowerCase() as 'full' | 'heavy',
          enableSmoothing: modelQuality === 'Full', // Enable smoothing for Full model accuracy
          enableSegmentation: false,
          smoothSegmentation: false,
          // TASK 6: Use versioned URLs from constants
          detectorModelUrl: BLAZEPOSE_MODEL_VERSIONS.detector.url,
          landmarkModelUrl: BLAZEPOSE_MODEL_VERSIONS.landmark[modelQuality.toLowerCase() as 'full' | 'heavy'].url
        });
        
        console.log('🔧 PHASE 5: Custom BlazePose detector loaded:', modelQuality);
        
        console.log('✅ PHASE 5: Custom BlazePose detector loaded successfully');
        
        setDetector(blazePoseDetector);
        setIsInitialized(true);
        console.log('✅ PHASE 5: Custom BlazePose initialization complete');
        
        setDebugInfo(`Phase 5: Custom BlazePose (${modelQuality}) - Complete Pipeline - ${tf.getBackend()}`);
        
      } catch (error) {
        console.error('❌ PHASE 5: Custom BlazePose initialization failed:', error);
        setDebugInfo(`Phase 5 Custom BlazePose Init Error: ${error.message}`);
        setIsInitialized(false);
        setDetector(null);
      }
    };

    initBlazePose();
    
    return () => {
      if (detector) {
        console.log('🧹 PHASE 5: Cleaning up custom BlazePose detector');
        try {
          detector.dispose();
        } catch (disposeError) {
          console.warn('⚠️ Error disposing custom BlazePose detector:', disposeError);
        }
      }
    };
  }, [modelQuality]);

  const detectPoses = async (video: HTMLVideoElement) => {
    console.log('🔍 PHASE 5: Running custom BlazePose with complete pipeline');
    
    if (!detector || !video || !isInitialized) {
      console.log('❌ PHASE 5: Custom detector or video not ready');
      return [];
    }

    if (video.readyState < 2 || video.videoWidth === 0 || video.videoHeight === 0) {
      console.log('⏳ PHASE 5: Video not ready for processing');
      return [];
    }

    try {
      console.log('🚀 PHASE 5: Custom BlazePose with complete pipeline...');
      
      const imageSize = getImageSize(video);
      console.log('📐 PHASE 5: Video dimensions:', imageSize);
      
      // Use our custom BlazePose detector
      const estimationConfig = {
        maxPoses: 1,
        flipHorizontal: false
      };
      
      const poses = await detector.estimatePoses(video, estimationConfig);
      
      console.log(`✅ PHASE 5: Custom BlazePose detected ${poses.length} poses with complete pipeline`);
      
      if (poses.length > 0) {
        const pose = poses[0];
        
        // PHASE 5: Apply enhanced processing with complete pipeline
        const processedPose = await processBlazePoseWithCompletePipeline(pose, imageSize);
        
        console.log('🎯 PHASE 5: COMPLETE PIPELINE POSE AUDIT:');
        
        if (processedPose.keypoints && processedPose.keypoints.length > 0) {
          const sampleKeypoint = processedPose.keypoints[0];
          console.log('✅ COMPLETE PIPELINE 2D KEYPOINTS:', {
            count: processedPose.keypoints.length,
            sample: {
              name: sampleKeypoint.name || 'unknown',
              x: sampleKeypoint.x?.toFixed(2),
              y: sampleKeypoint.y?.toFixed(2),
              score: sampleKeypoint.score?.toFixed(3),
              isValid: !isNaN(sampleKeypoint.x) && !isNaN(sampleKeypoint.y)
            }
          });
        }
        
        if (processedPose.keypoints3D && processedPose.keypoints3D.length > 0) {
          const sample3D = processedPose.keypoints3D[0];
          console.log('✅ COMPLETE PIPELINE 3D WORLD LANDMARKS:', {
            count: processedPose.keypoints3D.length,
            sample: {
              name: sample3D.name || 'unknown',
              x: sample3D.x?.toFixed(4),
              y: sample3D.y?.toFixed(4),
              z: sample3D.z?.toFixed(4),
              isValid: !isNaN(sample3D.x) && !isNaN(sample3D.y) && !isNaN(sample3D.z)
            }
          });
        }
        
        return [processedPose];
      } else {
        console.log('⚠️ PHASE 5: No poses detected');
        return [];
      }
      
    } catch (error) {
      console.error('❌ PHASE 5: Custom BlazePose detection error:', error);
      setDebugInfo(`Phase 5 Custom BlazePose Detection Error: ${error.message}`);
      return [];
    }
  };

  // PHASE 5: Complete pipeline processing with proper keypoint naming
  const processBlazePoseWithCompletePipeline = async (pose: any, imageSize: any) => {
    console.log('🔧 PHASE 5: Applying complete pipeline processing...');
    
    try {
      let processedKeypoints = pose.keypoints;
      let processedKeypoints3D = pose.keypoints3D;
      
      // Step 1: Ensure proper keypoint naming
      if (processedKeypoints) {
        processedKeypoints = processedKeypoints.map((kp: any, index: number) => ({
          ...kp,
          name: kp.name || BLAZEPOSE_KEYPOINTS[index] || `landmark_${index}`
        }));
      }
      
      if (processedKeypoints3D) {
        processedKeypoints3D = processedKeypoints3D.map((kp: any, index: number) => ({
          ...kp,
          name: kp.name || BLAZEPOSE_KEYPOINTS[index] || `world_landmark_${index}`
        }));
      }
      
      // Step 2: Apply coordinate validation and cleaning
      if (processedKeypoints || processedKeypoints3D) {
        const { landmarks, worldLandmarks } = validateAndCleanPoseCoordinates(
          processedKeypoints || [],
          processedKeypoints3D || []
        );
        
        processedKeypoints = landmarks;
        processedKeypoints3D = worldLandmarks;
        console.log('✅ PHASE 5: Applied complete coordinate validation and cleaning');
      }
      
      // Step 3: Enhanced keypoint processing with proper scaling
      if (processedKeypoints && processedKeypoints.length > 0) {
        console.log('🔧 PHASE 5: Complete keypoint processing...');
        
        // Calculate and smooth ROI
        const currentROI = calculateROIFromLandmarks(processedKeypoints, imageSize, 1.3);
        
        if (validateROI(currentROI, imageSize)) {
          const smoothedROI = roiFilter.apply(currentROI);
          processedKeypoints = calculateLandmarkProjection(processedKeypoints, smoothedROI);
        }
        
        // Check if coordinates need scaling (0-1 range to pixels)
        const needsScaling = processedKeypoints.some((kp: any) => 
          kp.x <= 1.0 && kp.y <= 1.0 && kp.x >= 0.0 && kp.y >= 0.0
        );
        
        if (needsScaling) {
          processedKeypoints = normalizedKeypointsToKeypoints(processedKeypoints, imageSize);
          console.log('✅ PHASE 5: Keypoints scaled to pixel coordinates');
        }
        
        // Apply visibility threshold filtering - PHASE 5 enhancement
        processedKeypoints = processedKeypoints.map((kp: any) => ({
          ...kp,
          score: Math.max(0, Math.min(1, kp.score || 0)),
          visible: (kp.score || 0) > 0.2 // Enhanced visibility threshold
        }));
        
        // Apply enhanced smoothing
        processedKeypoints = keypointsFilter.apply(processedKeypoints);
        console.log('✅ PHASE 5: Applied complete keypoints smoothing with visibility');
        
        // Apply stability filter
        const stabilityResult = stabilityFilter.apply(processedKeypoints);
        processedKeypoints = stabilityResult.keypoints;
        
        console.log('✅ PHASE 5: Applied pose stability filter:', {
          isStable: stabilityResult.isStable,
          confidence: stabilityResult.confidence.toFixed(3),
          visibleKeypoints: processedKeypoints.filter((kp: any) => kp.visible).length
        });
      }
      
      // Step 4: Complete 3D world landmarks processing
      if (processedKeypoints3D && processedKeypoints3D.length > 0) {
        console.log('🔧 PHASE 5: Complete 3D world landmarks processing...');
        
        const enhancedROI = processedKeypoints ? 
          calculateROIFromLandmarks(processedKeypoints, imageSize, 1.5) :
          {
            xCenter: imageSize.width / 2,
            yCenter: imageSize.height / 2,
            width: imageSize.width,
            height: imageSize.height,
            rotation: 0
          };
        
        const expandedROI = expandROI(enhancedROI, 1.2, imageSize);
        
        processedKeypoints3D = calculateWorldLandmarkProjection(processedKeypoints3D, expandedROI);
        console.log('✅ PHASE 5: Applied complete 3D world landmark projection');
        
        // Apply visibility thresholding to 3D landmarks
        processedKeypoints3D = processedKeypoints3D.map((kp: any) => ({
          ...kp,
          score: Math.max(0, Math.min(1, kp.score || 0)),
          visible: (kp.score || 0) > 0.1 // Lower threshold for 3D landmarks
        }));
        
        processedKeypoints3D = visibilityFilter.apply(processedKeypoints3D);
        console.log('✅ PHASE 5: Applied complete visibility smoothing to 3D landmarks');
      }
      
      const processedPose = {
        ...pose,
        keypoints: processedKeypoints,
        keypoints3D: processedKeypoints3D
      };
      
      console.log('✅ PHASE 5: Complete pipeline processing finished');
      return processedPose;
      
    } catch (processingError) {
      console.error('❌ PHASE 5 PROCESSING: Complete pipeline processing error:', processingError);
      return pose;
    }
  };

  const resetDetector = () => {
    if (detector && typeof detector.reset === 'function') {
      console.log('🔄 PHASE 5: Resetting custom BlazePose detector and filters');
      try {
        detector.reset();
        keypointsFilter.reset();
        visibilityFilter.reset();
        roiFilter.reset();
        stabilityFilter.reset();
        console.log('✅ Custom BlazePose detector and filters reset successful');
      } catch (resetError) {
        console.warn('⚠️ Error resetting custom BlazePose detector:', resetError);
      }
    }
  };

  return {
    detector,
    isInitialized,
    debugInfo,
    setDebugInfo,
    detectPoses,
    resetDetector
  };
};
