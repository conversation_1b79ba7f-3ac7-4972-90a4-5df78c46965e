/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

// Expected test landmarks for MediaPipe tests
export const EXPECTED_LANDMARKS = [
  // Sample landmark data for testing - this would be replaced with actual test data
  [400, 300], [410, 290], [390, 290], [380, 285], [420, 285], [420, 295], [380, 295]
  // ... more landmarks would be here in a real implementation
];

export const EXPECTED_WORLD_LANDMARKS = [
  // Sample world landmark data for testing
  [0.1, 0.2, 0.3], [0.15, 0.18, 0.32], [0.05, 0.18, 0.32]
  // ... more world landmarks would be here in a real implementation
];