/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

/**
 * BlazePose MediaPipe types and configurations.
 */

export type BlazePoseModelType = 'lite'|'full'|'heavy';

export interface BlazePoseModelConfig {
  runtime?: 'mediapipe'|'tfjs';
  enableSmoothing?: boolean;
  enableSegmentation?: boolean;
  smoothSegmentation?: boolean;
  modelType?: BlazePoseModelType;
}

export interface BlazePoseEstimationConfig {
  maxPoses?: number;
  flipHorizontal?: boolean;
  staticImageMode?: boolean;
}

export interface BlazePoseMediapipeModelConfig extends BlazePoseModelConfig {
  runtime: 'mediapipe';
  solutionPath?: string;
}

export interface BlazePoseMediapipeEstimationConfig extends BlazePoseEstimationConfig {
  // MediaPipe specific configs
}