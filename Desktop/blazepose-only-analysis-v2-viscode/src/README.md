# Active BlazePose Pipeline

## Current Active Implementation

This project now uses a **single, unified BlazePose custom pipeline** with the following active components:

### Active Hooks ✅
- **`src/hooks/useBlazePoseDetection.ts`** - The main custom BlazePose hook with complete processing pipeline
- Includes enhanced tensor processing, coordinate validation, and smoothing filters

### Active Components ✅
- **`src/components/SideViewBlazePoseOverlay.tsx`** - Side view pose overlay using custom BlazePose
- Enhanced with PHASE 6F debugging and custom tensor processing

### Active Processors ✅
- **`src/shared/calculators/blazepose_tensor_processor.ts`** - Enhanced tensor processing with PHASE 6G debugging
- Custom coordinate validation and NaN fixing

## Archived Components 📁

The following components have been moved to `src/archived/` and are **DEPRECATED**:

### Archived MoveNet Implementation
- `useMoveNetDetection.ts` - Old MoveNet-based hook
- `RunningPoseOverlay.tsx` - MoveNet running pose component
- `SideViewPoseOverlay.tsx` - MoveNet side view component  
- `RearViewPoseOverlay.tsx` - MoveNet rear view component

### Archived BlazePose Lite Implementation
- `useBlazePoseLite.ts` - Basic TensorFlow.js BlazePose hook
- `usePoseDetection.ts` - Old mixed-implementation hook

### Archived Constants
- `constants.ts` - Old constants file (replaced by shared/calculators/blazepose_constants.ts)

## Pipeline Status

✅ **ACTIVE**: Custom BlazePose implementation with enhanced tensor processing  
❌ **DEPRECATED**: All MoveNet and BlazePose Lite implementations  

**WARNING**: Do not use any files from the `src/archived/` directory. They are kept for reference only.

## Next Steps

Now that the pipeline is cleaned up, the custom BlazePose implementation should be working with:
- Enhanced tensor processing (PHASE 6G)
- Coordinate validation and NaN fixing
- Complete debugging infrastructure
- Unified hook architecture