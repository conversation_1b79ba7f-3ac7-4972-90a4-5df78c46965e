
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/Header';
import UploadPanel from '@/components/UploadPanel';
import ConfigurationPanel from '@/components/ConfigurationPanel';
import ProcessingPanel from '@/components/ProcessingPanel';
import ResultsPanel from '@/components/ResultsPanel';

type AppState = 'upload' | 'processing' | 'results';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

const Index = () => {
  const [currentState, setCurrentState] = useState<AppState>('upload');
  const [sideVideo, setSideVideo] = useState<VideoFile | null>(null);
  const [rearVideo, setRearVideo] = useState<VideoFile | null>(null);
  const [height, setHeight] = useState({ feet: 5, inches: 10 });

  // Fixed values for BlazePose Full implementation
  const analysisType = 'running';
  const analysisMode = '3D';
  const activityType = 'Running';
  const videoSetup = 'Treadmill';
  const viewType = 'side'; 
  const overlayStyle = 'Medical';
  const analysisQuality = 'Full';

  const canStartAnalysis = sideVideo !== null;

  const handleStartAnalysis = () => {
    if (canStartAnalysis) {
      console.log('🚀 STARTING BLAZEPOSE FULL ANALYSIS:');
      console.log('analysisMode:', analysisMode);
      console.log('activityType:', activityType);
      console.log('videoSetup:', videoSetup);
      console.log('analysisQuality:', analysisQuality);
      console.log('userHeight:', height);
      
      setCurrentState('processing');
      // Simulate processing time
      setTimeout(() => {
        setCurrentState('results');
      }, 3000);
    }
  };

  const handleNewAnalysis = () => {
    setCurrentState('upload');
    setSideVideo(null);
    setRearVideo(null);
  };

  if (currentState === 'processing') {
    return (
      <>
        <Header 
          analysisMode={analysisMode}
          onAnalysisModeChange={() => {}} // No-op since mode is fixed
          activityType={activityType}
          onActivityTypeChange={() => {}} // No-op since type is fixed
          isLocked={true}
        />
        <ProcessingPanel analysisMode={analysisMode} />
      </>
    );
  }

  if (currentState === 'results') {
    return (
      <>
        <Header 
          analysisMode={analysisMode}
          onAnalysisModeChange={() => {}} // No-op since mode is fixed
          activityType={activityType}
          onActivityTypeChange={() => {}} // No-op since type is fixed
          isLocked={true}
        />
        <ResultsPanel 
          sideVideo={sideVideo} 
          rearVideo={rearVideo} 
          onNewAnalysis={handleNewAnalysis}
          analysisMode={analysisMode}
          videoSetup={videoSetup}
          overlayStyle={overlayStyle}
          analysisQuality={analysisQuality}
          userHeight={height}
        />
      </>
    );
  }

  return (
    <>
      <Header 
        analysisMode={analysisMode}
        onAnalysisModeChange={() => {}} // No-op since mode is fixed
        activityType={activityType}
        onActivityTypeChange={() => {}} // No-op since type is fixed
        isLocked={false}
      />
      
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold text-gray-900">
              3D Running Analysis - BlazePose Full
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Upload your treadmill videos for 3D biomechanical analysis using BlazePose Full.
              Record from 5 feet away for optimal results.
            </p>
          </div>

          {/* Upload Panels - Side and Rear Views */}
          <div className="grid md:grid-cols-2 gap-6">
            <UploadPanel
              title="Upload Side View Video"
              description="Record from the side to capture leg extension and body lean"
              acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
              video={sideVideo}
              onVideoUpload={setSideVideo}
            />
            <UploadPanel
              title="Upload Rear View Video"
              description="Record from behind to analyze stride symmetry and foot placement"
              acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
              video={rearVideo}
              onVideoUpload={setRearVideo}
            />
          </div>

          {/* Configuration Panel - Simplified */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Analysis Configuration</h3>
              <div className="grid md:grid-cols-3 gap-6">
                {/* Height Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Height
                  </label>
                  <div className="flex gap-2">
                    <select
                      value={height.feet}
                      onChange={(e) => setHeight({ ...height, feet: parseInt(e.target.value) })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {[4, 5, 6, 7].map(ft => (
                        <option key={ft} value={ft}>{ft} ft</option>
                      ))}
                    </select>
                    <select
                      value={height.inches}
                      onChange={(e) => setHeight({ ...height, inches: parseInt(e.target.value) })}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {[...Array(12)].map((_, i) => (
                        <option key={i} value={i}>{i} in</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Fixed Configuration Display */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Setup
                  </label>
                  <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600">
                    Treadmill (5ft distance)
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Model
                  </label>
                  <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded-md text-gray-600">
                    BlazePose Full
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Start Analysis Button */}
          <Card>
            <CardContent className="p-6 text-center">
              <button
                onClick={handleStartAnalysis}
                disabled={!canStartAnalysis}
                className={`inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold transition-all ${
                  canStartAnalysis
                    ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <div className="w-6 h-6 rounded-full border-2 border-current flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-current"></div>
                </div>
                Start 3D Analysis (BlazePose Full)
              </button>
              {!canStartAnalysis && (
                <p className="text-sm text-gray-500 mt-2">
                  Please upload at least a side view video to begin analysis
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default Index;
