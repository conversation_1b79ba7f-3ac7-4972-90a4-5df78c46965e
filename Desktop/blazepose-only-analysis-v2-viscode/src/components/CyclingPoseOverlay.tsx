
import React from 'react';

interface CyclingPoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
}

const CyclingPoseOverlay: React.FC<CyclingPoseOverlayProps> = ({ 
  videoRef, 
  onPoseData 
}) => {
  // Future implementation for cycling pose analysis
  // Will include different keypoint connections and biomechanics
  
  return (
    <div className="absolute top-4 left-4 bg-blue-500/80 text-white px-3 py-1 rounded text-sm">
      Cycling Analysis - Coming Soon
    </div>
  );
};

export default CyclingPoseOverlay;
