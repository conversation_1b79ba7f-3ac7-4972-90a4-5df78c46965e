
import React, { useRef, useEffect, useState } from 'react';
import { useBlazePoseDetection } from '@/hooks/useBlazePoseDetection';

interface BlazePoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
  testMode?: boolean; // NEW: Enable test mode with non-clear canvas
}

const BlazePoseOverlay: React.FC<BlazePoseOverlayProps> = ({ 
  videoRef, 
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 },
  onPoseData,
  testMode = true // Enable test mode by default to see overlay
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [frameCount, setFrameCount] = useState(0);
  
  // SEALED 3D PIPELINE: Use dedicated BlazePose hook with official patterns
  const { detector, isInitialized, debugInfo, setDebugInfo, detectPoses } = useBlazePoseDetection(modelQuality);

  // Convert user height to meters for precise calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    console.log('🔍 BLAZEPOSE OVERLAY PHASE 5 COMPLETE PIPELINE AUDIT:');
    console.log('✅ PHASE 5 COMPLETE: This component uses our CUSTOM BlazePose detector');
    console.log('🔧 PHASE 5 COMPLETE: Complete tensor processing pipeline implemented');
    console.log('🎯 Model Quality:', modelQuality);
    console.log('📍 Video Setup:', videoSetup);
    console.log('👤 User Height:', `${userHeight.feet}'${userHeight.inches}"`, `(${userHeightMeters.toFixed(2)}m)`);
    console.log('🧪 TEST MODE:', testMode ? 'ENABLED (Non-Clear Canvas for Visibility)' : 'DISABLED (Normal Operation)');
    
    // PHASE 5 AUDIT: Check for complete pipeline capabilities
    console.log('🔍 PHASE 5 AUDIT: Custom BlazePose complete pipeline capabilities...');
    console.log('✅ PHASE 5 AUDIT: Custom detector provides enhanced keypoints3D with proper naming');
    console.log('✅ PHASE 5 AUDIT: Enhanced 2D keypoints with visibility filtering');
    console.log('✅ PHASE 5 AUDIT: Complete coordinate scaling and projection pipeline');
    console.log('✅ PHASE 5 AUDIT: Proper keypoint visibility and confidence handling');
    
    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      console.log('⏸️ SEALED 3D PIPELINE: BlazePose not ready yet - waiting for initialization');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    const processFrame = async () => {
      if (video.paused || video.ended) {
        animationFrameRef.current = requestAnimationFrame(processFrame);
        return;
      }

      try {
        setIsProcessing(true);
        
        // Set canvas size to match video display size
        const rect = video.getBoundingClientRect();
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
          console.log('🎨 Canvas dimensions set:', { width: canvas.width, height: canvas.height });
        }
        
        // PHASE 6A: Always draw canvas test indicators first
        if (testMode) {
          // Add semi-transparent overlay to see accumulation
          ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          
          // PHASE 6A: Draw animated test indicator to confirm canvas is working
          const time = Date.now() * 0.001;
          const testX = 50 + Math.sin(time) * 20;
          const testY = 50 + Math.cos(time) * 20;
          
          ctx.fillStyle = '#FF0000';
          ctx.fillRect(testX, testY, 10, 10);
          ctx.fillStyle = '#FFFFFF';
          ctx.font = '14px Arial';
          ctx.fillText('CANVAS ACTIVE', testX + 15, testY + 8);
          
          console.log('🧪 PHASE 6A: Canvas test indicator drawn at:', { x: testX.toFixed(1), y: testY.toFixed(1) });
        } else {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // PHASE 5 COMPLETE PIPELINE: Use our custom BlazePose detector with complete processing
        console.log('🔍 PHASE 5 COMPLETE: Calling custom BlazePose detectPoses...');
        const poses = await detectPoses(video);
        console.log(`🚀 PHASE 5 COMPLETE: Custom BlazePose returned ${poses.length} poses`);
        
        // PHASE 5 COMPLETE PIPELINE: Enhanced pose processing and visibility
        if (poses.length > 0) {
          const pose = poses[0];
          console.log(`🎯 PHASE 5 COMPLETE: Processing pose with ${pose.keypoints.length} keypoints`);
          
          // PHASE 5 AUDIT: Check for enhanced 3D world landmarks
          if (pose.keypoints3D && pose.keypoints3D.length > 0) {
            const visibleWorldLandmarks = pose.keypoints3D.filter((kp: any) => kp.visible !== false);
            console.log('✅ PHASE 5 AUDIT: Enhanced 3D World landmarks:', {
              total: pose.keypoints3D.length,
              visible: visibleWorldLandmarks.length,
              samplePoint: {
                name: pose.keypoints3D[0].name || 'unknown',
                x: pose.keypoints3D[0].x?.toFixed(3),
                y: pose.keypoints3D[0].y?.toFixed(3),
                z: pose.keypoints3D[0].z?.toFixed(3),
                score: pose.keypoints3D[0].score?.toFixed(3),
                visible: pose.keypoints3D[0].visible !== false,
                coordinates: '3D world coordinates in meters'
              }
            });
          } else {
            console.log('⚠️ PHASE 5 AUDIT: No 3D world landmarks found');
          }
          
          // PHASE 5 AUDIT: Check enhanced 2D keypoints with visibility
          if (pose.keypoints && pose.keypoints.length > 0) {
            const visibleKeypoints = pose.keypoints.filter((kp: any) => kp.visible !== false);
            console.log('✅ PHASE 5 AUDIT: Enhanced 2D keypoints with visibility:', {
              total: pose.keypoints.length,
              visible: visibleKeypoints.length,
              samplePoint: {
                name: pose.keypoints[0].name || 'unknown',
                x: Math.round(pose.keypoints[0].x),
                y: Math.round(pose.keypoints[0].y),
                score: pose.keypoints[0].score?.toFixed(3),
                visible: pose.keypoints[0].visible !== false,
                coordinates: '2D screen coordinates in pixels'
              }
            });
          }
          
          drawPoseWithEnhancedHeightScaling(ctx, pose, canvas.width, canvas.height, testMode);
          
          // Send enhanced pose data to parent component
          if (onPoseData) {
            const visibleKeypoints = pose.keypoints?.filter((kp: any) => kp.visible !== false) || [];
            const visibleWorldLandmarks = pose.keypoints3D?.filter((kp: any) => kp.visible !== false) || [];
            
            onPoseData({
              frameNumber: frameCount,
              timestamp: Date.now(),
              pose: pose,
              has3D: !!(pose.keypoints3D && pose.keypoints3D.length > 0),
              visibilityStats: {
                total2D: pose.keypoints?.length || 0,
                visible2D: visibleKeypoints.length,
                total3D: pose.keypoints3D?.length || 0,
                visible3D: visibleWorldLandmarks.length
              },
              modelQuality: modelQuality,
              videoSetup: videoSetup,
              userHeight: userHeight,
              pipeline: 'Phase-5-Custom-BlazePose-Complete',
              testMode: testMode
            });
          }
        } else {
          console.log('⚠️ PHASE 5 COMPLETE: No poses detected by custom BlazePose');
          
          // TEST MODE: Draw a debug indicator when no poses detected
          if (testMode) {
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.fillRect(10, 10, 20, 20);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('No Pose', 35, 25);
          }
        }
        
        setFrameCount(prev => prev + 1);
        setDebugInfo(`Phase 5 Frame ${frameCount}: Custom BlazePose (${modelQuality}) - ${videoSetup} - ${userHeight.feet}'${userHeight.inches}" - COMPLETE PIPELINE${testMode ? ' - TEST MODE' : ''}`);
        
      } catch (error) {
        console.error('❌ PHASE 5 COMPLETE: Custom BlazePose processing error:', error);
        setDebugInfo(`Phase 5 BlazePose Error: ${error.message}`);
      } finally {
        setIsProcessing(false);
      }

      animationFrameRef.current = requestAnimationFrame(processFrame);
    };

    console.log('✅ PHASE 5 COMPLETE: Starting custom BlazePose processing loop with complete pipeline');
    processFrame();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isInitialized, videoRef, modelQuality, videoSetup, frameCount, onPoseData, detectPoses, setDebugInfo, userHeightMeters, userHeight, testMode]);

  const calculateEnhancedHeightScale = (keypoints: any[], canvasHeight: number) => {
    console.log('📏 SEALED 3D PIPELINE: Enhanced height calculation with better fallbacks');
    
    // Enhanced keypoint validation with multiple confidence thresholds
    const getValidKeypoint = (index: number, minScore = 0.3) => {
      const kp = keypoints[index];
      return kp && kp.score >= minScore ? kp : null;
    };
    
    // Primary approach: Full body height (nose to ankles)
    const nose = getValidKeypoint(0, 0.2);           // Head reference (lower threshold)
    const leftAnkle = getValidKeypoint(27, 0.2);     // Left ankle  
    const rightAnkle = getValidKeypoint(28, 0.2);    // Right ankle
    
    let apparentHeightPixels = 0;
    let method = 'fallback';
    
    if (nose && (leftAnkle || rightAnkle)) {
      // Use best available ankle
      const ankle = (leftAnkle && rightAnkle) ? 
        { y: (leftAnkle.y + rightAnkle.y) / 2 } : 
        (leftAnkle || rightAnkle);
      
      apparentHeightPixels = Math.abs(ankle.y - nose.y);
      method = 'nose-to-ankle';
      console.log(`📏 SEALED 3D PIPELINE: Using ${method}: ${apparentHeightPixels.toFixed(1)}px`);
    } 
    // Secondary approach: Shoulders to hips (torso-based estimation)
    else {
      const leftShoulder = getValidKeypoint(11, 0.3);
      const rightShoulder = getValidKeypoint(12, 0.3);
      const leftHip = getValidKeypoint(23, 0.3);
      const rightHip = getValidKeypoint(24, 0.3);
      
      if ((leftShoulder || rightShoulder) && (leftHip || rightHip)) {
        const shoulderY = (leftShoulder && rightShoulder) ? 
          (leftShoulder.y + rightShoulder.y) / 2 :
          (leftShoulder || rightShoulder).y;
        
        const hipY = (leftHip && rightHip) ? 
          (leftHip.y + rightHip.y) / 2 :
          (leftHip || rightHip).y;
        
        const torsoHeight = Math.abs(hipY - shoulderY);
        
        // Torso is approximately 35-40% of total height
        apparentHeightPixels = torsoHeight / 0.37;
        method = 'torso-estimation';
        console.log(`📏 SEALED 3D PIPELINE: Using ${method}: ${apparentHeightPixels.toFixed(1)}px`);
      }
      // Tertiary approach: Use canvas height as reasonable estimate
      else {
        apparentHeightPixels = canvasHeight * 0.75; // Assume person takes 75% of frame
        method = 'canvas-fallback';
        console.log(`📏 SEALED 3D PIPELINE: Using ${method}: ${apparentHeightPixels.toFixed(1)}px`);
      }
    }
    
    // Enhanced distance calculation based on video setup
    let referenceDistance = 5; // feet - baseline for all setups
    
    if (videoSetup === 'Treadmill') {
      referenceDistance = 5; // Constant 5 feet
    } else if (videoSetup === 'OutAndIn' || videoSetup === 'OffsetOutAndIn') {
      // Dynamic distance could be implemented here based on video timeline
      referenceDistance = 5; // Using start distance for now
    }
    
    // Expected height in pixels at reference distance (enhanced FOV calculation)
    const referenceHeightPixels = canvasHeight * 0.65; // Refined estimate
    
    // Calculate actual distance and scale factor
    const actualDistance = referenceDistance * (referenceHeightPixels / apparentHeightPixels);
    const scaleFactor = referenceDistance / actualDistance;
    const clampedScale = Math.max(0.4, Math.min(1.8, scaleFactor)); // More reasonable bounds
    
    console.log(`📏 SEALED 3D PIPELINE: Enhanced height scaling:`, {
      method: method,
      userHeight: `${userHeight.feet}'${userHeight.inches}"`,
      userHeightMeters: userHeightMeters.toFixed(2),
      apparentPixels: apparentHeightPixels.toFixed(1),
      referencePixels: referenceHeightPixels.toFixed(1),
      calculatedDistance: actualDistance.toFixed(1),
      scaleFactor: clampedScale.toFixed(2),
      videoSetup: videoSetup
    });
    
    return clampedScale;
  };

  const drawPoseWithEnhancedHeightScaling = (ctx: CanvasRenderingContext2D, pose: any, width: number, height: number, testMode: boolean = false) => {
    const keypoints = pose.keypoints;
    const scale = calculateEnhancedHeightScale(keypoints, height);
    
    console.log(`🎨 PHASE 6C: Drawing BlazePose with enhanced scale ${scale.toFixed(2)}${testMode ? ' (TEST MODE)' : ''}`);
    console.log(`🎨 PHASE 6C: Canvas dimensions: ${width}x${height}`);
    console.log(`🎨 PHASE 6C: Raw keypoints passed to drawing:`, keypoints?.length || 0);
    
    // PHASE 6C: Enhanced canvas verification with multiple debug indicators
    if (testMode) {
      // Canvas border verification
      ctx.strokeStyle = '#FF00FF'; // Magenta border for canvas debugging
      ctx.lineWidth = 5;
      ctx.strokeRect(0, 0, width, height);
      
      // Corner verification markers
      const cornerSize = 20;
      ctx.fillStyle = '#FF0000'; // Red corners
      ctx.fillRect(0, 0, cornerSize, cornerSize); // Top-left
      ctx.fillRect(width - cornerSize, 0, cornerSize, cornerSize); // Top-right
      ctx.fillRect(0, height - cornerSize, cornerSize, cornerSize); // Bottom-left
      ctx.fillRect(width - cornerSize, height - cornerSize, cornerSize, cornerSize); // Bottom-right
      
      // Center verification marker
      ctx.fillStyle = '#00FF00'; // Green center
      ctx.fillRect(width/2 - 10, height/2 - 10, 20, 20);
      
      // Drawing pipeline status
      ctx.fillStyle = '#FFFF00'; // Yellow background for status
      ctx.fillRect(10, height - 80, 300, 70);
      ctx.fillStyle = '#000000'; // Black text
      ctx.font = 'bold 12px Arial';
      ctx.fillText(`PHASE 6C: DRAWING PIPELINE VERIFICATION`, 15, height - 65);
      ctx.fillText(`Canvas: ${width}x${height} | Scale: ${scale.toFixed(2)}`, 15, height - 50);
      ctx.fillText(`Keypoints: ${keypoints?.length || 0} | Valid: TBD`, 15, height - 35);
      ctx.fillText(`Context: ${ctx ? 'ACTIVE' : 'NULL'} | TestMode: ${testMode}`, 15, height - 20);
      
      console.log('🎨 PHASE 6C: Drew comprehensive canvas verification indicators');
    }
    
    // TEST MODE: Use brighter colors for better visibility in accumulating canvas
    const colorMultiplier = testMode ? 1.5 : 1.0;
    
    console.log(`🎨 PHASE 6A: Processing ${keypoints.length} keypoints for drawing`);
    let drawnKeypoints = 0;
    
    // PHASE 6C: Detailed keypoint validation and drawing pipeline verification
    let validCoordCount = 0;
    let visibleCount = 0;
    let drawnCount = 0;
    
    // Draw keypoints with enhanced scaling, confidence-based sizing, and visibility filtering
    keypoints.forEach((keypoint: any, index: number) => {
      // PHASE 6C: Comprehensive coordinate validation
      const hasValidCoords = !isNaN(keypoint.x) && !isNaN(keypoint.y) && 
                            isFinite(keypoint.x) && isFinite(keypoint.y) &&
                            keypoint.x >= 0 && keypoint.y >= 0 && 
                            keypoint.x <= width && keypoint.y <= height;
      
      // PHASE 6C: Enhanced visibility check with explicit logging
      const isVisible = keypoint.visible !== false && keypoint.score > 0.2;
      
      if (hasValidCoords) validCoordCount++;
      if (isVisible) visibleCount++;
      
      // PHASE 6C: Comprehensive debugging for first 10 keypoints
      if (index < 10) {
        console.log(`🎨 PHASE 6C: Keypoint ${index} (${keypoint.name || 'unnamed'}):`, {
          coords: { 
            x: keypoint.x?.toFixed(1), 
            y: keypoint.y?.toFixed(1),
            valid: hasValidCoords 
          },
          score: keypoint.score?.toFixed(3),
          visible: keypoint.visible,
          isVisible: isVisible,
          willDraw: isVisible && hasValidCoords
        });
      }
      
      if (isVisible && hasValidCoords) {
        // PHASE 6C: Enhanced drawing with better visibility
        const baseRadius = Math.max(5, 8 * scale); // Increased minimum radius for better visibility
        const confidenceMultiplier = Math.max(0.5, keypoint.score);
        const radius = baseRadius * confidenceMultiplier;
        
        // PHASE 6C: Use high-contrast colors in test mode
        const keypointColor = testMode ? 
          (index % 2 === 0 ? '#FF0000' : '#00FF00') : // Alternating red/green in test mode
          getEnhancedKeypointColor(index, keypoint.score, colorMultiplier);
        
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, radius, 0, 2 * Math.PI);
        ctx.fillStyle = keypointColor;
        ctx.fill();
        
        // PHASE 6C: High-contrast border for visibility
        ctx.strokeStyle = testMode ? '#FFFFFF' : '#000000';
        ctx.lineWidth = Math.max(2, 3 * scale);
        ctx.stroke();
        
        drawnCount++;
        
        // PHASE 6C: Enhanced keypoint labels with index numbers
        if (testMode) {
          ctx.fillStyle = '#FFFFFF';
          ctx.font = 'bold 14px Arial';
          ctx.fillText(`${index}`, keypoint.x - 8, keypoint.y + 5);
          
          if (keypoint.name) {
            ctx.font = '10px Arial';
            ctx.fillText(keypoint.name.slice(0, 6), keypoint.x + radius + 2, keypoint.y - radius);
          }
        }
      }
    });

    // PHASE 6C: Update drawing pipeline status with actual counts
    if (testMode) {
      ctx.fillStyle = '#FFFF00';
      ctx.fillRect(10, height - 80, 350, 70);
      ctx.fillStyle = '#000000';
      ctx.font = 'bold 12px Arial';
      ctx.fillText(`PHASE 6C: DRAWING PIPELINE VERIFICATION`, 15, height - 65);
      ctx.fillText(`Canvas: ${width}x${height} | Scale: ${scale.toFixed(2)}`, 15, height - 50);
      ctx.fillText(`Total: ${keypoints.length} | Valid: ${validCoordCount} | Visible: ${visibleCount} | Drawn: ${drawnCount}`, 15, height - 35);
      ctx.fillText(`Context: ${ctx ? 'ACTIVE' : 'NULL'} | TestMode: ${testMode}`, 15, height - 20);
    }

    console.log(`🎨 PHASE 6C: Drew ${drawnCount}/${keypoints.length} keypoints (Valid: ${validCoordCount}, Visible: ${visibleCount})`);

    // Draw pose connections with enhanced scaling
    drawEnhancedBlazePoseConnections(ctx, keypoints, scale, testMode);
  };

  const getEnhancedKeypointColor = (index: number, score: number, multiplier: number = 1.0): string => {
    const baseColors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#F8C471',
      '#82E0AA', '#F1948A'
    ];
    
    const baseColor = baseColors[index % baseColors.length];
    
    // Adjust opacity based on confidence and multiplier
    const alpha = Math.max(0.4, score * multiplier);
    const color = baseColor + Math.round(Math.min(255, alpha * 255)).toString(16).padStart(2, '0');
    
    return color;
  };

  const drawEnhancedBlazePoseConnections = (ctx: CanvasRenderingContext2D, keypoints: any[], scale: number, testMode: boolean = false) => {
    const connections = [
      [0, 1], [1, 2], [2, 3], [3, 7], // Face
      [0, 4], [4, 5], [5, 6], [6, 8], // Face
      [9, 10], // Mouth
      [11, 12], // Shoulders
      [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], // Left arm
      [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], // Right arm
      [11, 23], [12, 24], [23, 24], // Torso
      [23, 25], [25, 27], [27, 29], [27, 31], // Left leg
      [24, 26], [26, 28], [28, 30], [28, 32], // Right leg
    ];

    ctx.strokeStyle = testMode ? '#00FFFF' : '#00FF00'; // Brighter cyan in test mode
    ctx.lineWidth = Math.max(1, 2 * scale);
    ctx.globalAlpha = testMode ? 0.9 : 0.8; // More opaque in test mode

    let connectionsDrawn = 0;
    connections.forEach(([startIdx, endIdx]) => {
      const startPoint = keypoints[startIdx];
      const endPoint = keypoints[endIdx];

      // PHASE 5: Enhanced visibility and confidence check for connections
      const startVisible = startPoint && (startPoint.visible !== false) && startPoint.score > 0.2;
      const endVisible = endPoint && (endPoint.visible !== false) && endPoint.score > 0.2;

      if (startVisible && endVisible) {
        // Variable line width based on average confidence
        const avgConfidence = (startPoint.score + endPoint.score) / 2;
        ctx.lineWidth = Math.max(1, 2 * scale * avgConfidence);
        
        ctx.beginPath();
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(endPoint.x, endPoint.y);
        ctx.stroke();
        connectionsDrawn++;
      }
    });
    
    ctx.globalAlpha = 1.0; // Reset alpha
    console.log(`🎨 PHASE 5 COMPLETE: Drew ${connectionsDrawn}/${connections.length} enhanced connections with visibility${testMode ? ' (TEST MODE)' : ''}`);
  };

  const getSetupDisplayName = () => {
    switch(videoSetup) {
      case 'Treadmill': return 'Treadmill';
      case 'OutAndIn': return 'Out and In';
      case 'OffsetOutAndIn': return 'Offset Out and In';
      default: return videoSetup;
    }
  };

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 10,
          border: testMode ? '2px solid cyan' : 'none', // Visual indicator in test mode
        }}
      />
      <div 
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: testMode ? 'rgba(0,255,255,0.9)' : 'rgba(0,0,0,0.8)',
          color: testMode ? 'black' : 'cyan',
          padding: '8px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 11,
          border: testMode ? '2px solid yellow' : 'none'
        }}
      >
        🚀 PHASE 5 COMPLETE - Custom BlazePose ({modelQuality}) - COMPLETE PIPELINE
        {testMode && <><br />🧪 TEST MODE: Non-Clear Canvas + Labels (ENABLED)</>}
        <br />
        📍 Setup: {getSetupDisplayName()} (5ft start distance)
        <br />
        👤 User: {userHeight.feet}'{userHeight.inches}" ({userHeightMeters.toFixed(2)}m)
        <br />
        {isProcessing ? 'Processing...' : 'Ready'}
        <br />
        🎯 Visibility: Enhanced with explicit filtering
        <br />
        ✅ Custom Detector - Complete Tensor Pipeline
        <br />
        📊 Enhanced: Named keypoints + visibility + 3D world coords
        <br />
        {debugInfo}
      </div>
    </>
  );
};

export default BlazePoseOverlay;
