import React, { useRef, useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';
import PoseOverlay from './PoseOverlay';

interface VideoPlayerProps {
  videoUrl: string;
  analysisType: 'running';
  viewType: 'side' | 'rear';
  overlayStyle?: string;
  analysisMode: '3D';
  videoSetup: 'Treadmill';
  modelQuality?: 'Full' | 'Heavy';
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  videoUrl, 
  analysisType, 
  viewType,
  overlayStyle = 'Medical',
  analysisMode,
  videoSetup,
  modelQuality = 'Full',
  userHeight = { feet: 5, inches: 10 },
  onPoseData
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [playbackRate, setPlaybackRate] = useState(1);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => setCurrentTime(video.currentTime);
    const handleDurationChange = () => setDuration(video.duration);
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('durationchange', handleDurationChange);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('durationchange', handleDurationChange);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, []);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  };

  const restart = () => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = 0;
    video.play();
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = (parseFloat(e.target.value) / 100) * duration;
    video.currentTime = newTime;
  };

  const handleSpeedChange = (speed: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.playbackRate = speed;
    setPlaybackRate(speed);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
  const speedOptions = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2];

  console.log('🎥 VideoPlayer render - BLAZEPOSE FULL MODEL:');
  console.log('analysisMode:', analysisMode, 'type:', typeof analysisMode);
  console.log('analysisType:', analysisType, 'type:', typeof analysisType);
  console.log('viewType:', viewType, 'type:', typeof viewType);
  console.log('videoSetup:', videoSetup, 'type:', typeof videoSetup);
  console.log('modelQuality:', modelQuality, 'type:', typeof modelQuality);
  console.log('overlayStyle:', overlayStyle, 'type:', typeof overlayStyle);
  console.log('userHeight:', userHeight);

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="relative bg-black">
          <video
            ref={videoRef}
            src={videoUrl}
            className="w-full h-auto"
            onClick={togglePlay}
          />
          
          <PoseOverlay 
            videoRef={videoRef} 
            analysisType={analysisType}
            analysisMode={analysisMode}
            viewType={viewType}
            overlayStyle={overlayStyle}
            videoSetup={videoSetup}
            userHeight={userHeight}
            onPoseData={onPoseData}
          />
          
          {/* Video Controls */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
            <div className="flex items-center gap-4">
              <button
                onClick={togglePlay}
                className="flex items-center justify-center w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full transition-colors"
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5 text-white" />
                ) : (
                  <Play className="w-5 h-5 text-white ml-0.5" />
                )}
              </button>
              
              <button
                onClick={restart}
                className="flex items-center justify-center w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full transition-colors"
              >
                <RotateCcw className="w-4 h-4 text-white" />
              </button>
              
              {/* Speed Control */}
              <div className="flex items-center gap-2">
                <span className="text-white text-sm">Speed:</span>
                <select
                  value={playbackRate}
                  onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
                  className="bg-white/20 text-white text-sm rounded px-2 py-1 border-none outline-none"
                >
                  {speedOptions.map(speed => (
                    <option key={speed} value={speed} className="text-black">
                      {speed}x
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="flex-1 mx-4">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={progress}
                  onChange={handleProgressChange}
                  className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
              
              <div className="text-white text-sm font-mono">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
              
              <button
                onClick={toggleMute}
                className="flex items-center justify-center w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full transition-colors"
              >
                {isMuted ? (
                  <VolumeX className="w-4 h-4 text-white" />
                ) : (
                  <Volume2 className="w-4 h-4 text-white" />
                )}
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoPlayer;
