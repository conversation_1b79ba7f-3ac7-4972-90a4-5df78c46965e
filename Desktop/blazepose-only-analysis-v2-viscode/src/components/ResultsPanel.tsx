import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import VideoPlayer from '@/components/VideoPlayer';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

interface ResultsPanelProps {
  sideVideo: VideoFile | null;
  rearVideo: VideoFile | null;
  onNewAnalysis: () => void;
  analysisMode: '3D';
  videoSetup: 'Treadmill';
  overlayStyle: string;
  analysisQuality: string;
  userHeight: { feet: number; inches: number };
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({ 
  sideVideo, 
  rearVideo, 
  onNewAnalysis,
  analysisMode,
  videoSetup,
  overlayStyle,
  analysisQuality,
  userHeight
}) => {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-gray-900">
            3D Running Analysis Results - BlazePose Full
          </h1>
          <p className="text-lg text-gray-600">
            Biomechanical analysis of your treadmill running form using BlazePose Full
          </p>
        </div>

        {/* Video Analysis Grid */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Side View Analysis */}
          {sideVideo && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  Side View Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <VideoPlayer
                  videoUrl={sideVideo.url}
                  analysisType="running"
                  viewType="side"
                  analysisMode={analysisMode}
                  videoSetup={videoSetup}
                  overlayStyle={overlayStyle}
                  userHeight={userHeight}
                  onPoseData={(data) => console.log('Side view pose data:', data)}
                />
              </CardContent>
            </Card>
          )}

          {/* Rear View Analysis */}
          {rearVideo && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  Rear View Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <VideoPlayer
                  videoUrl={rearVideo.url}
                  analysisType="running"
                  viewType="rear"
                  analysisMode={analysisMode}
                  videoSetup={videoSetup}
                  overlayStyle={overlayStyle}
                  userHeight={userHeight}
                  onPoseData={(data) => console.log('Rear view pose data:', data)}
                />
              </CardContent>
            </Card>
          )}
        </div>

        {/* Analysis Panels (Placeholder) */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                Side View Metrics (Coming Soon)
              </h3>
              <p className="text-gray-600">
                Detailed biomechanical metrics from the side view video will be displayed here.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">
                Rear View Metrics (Coming Soon)
              </h3>
              <p className="text-gray-600">
                Detailed biomechanical metrics from the rear view video will be displayed here.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* New Analysis Button */}
        <div className="text-center">
          <button
            onClick={onNewAnalysis}
            className="inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all"
          >
            <RotateCcw className="w-5 h-5" />
            Start New Analysis
          </button>
        </div>
      </div>
    </div>
  );
};

import { RotateCcw } from 'lucide-react';
export default ResultsPanel;
