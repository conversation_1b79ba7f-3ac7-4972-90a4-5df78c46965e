
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface FootPronationData {
  underpronation: number;
  neutral: number;
  overpronation: number;
  dominantType: 'underpronation' | 'neutral' | 'overpronation';
}

interface PronationAnalysisProps {
  leftFootData?: FootPronationData;
  rightFootData?: FootPronationData;
}

const PronationAnalysis: React.FC<PronationAnalysisProps> = ({
  leftFootData = {
    underpronation: 11,
    neutral: 89,
    overpronation: 0,
    dominantType: 'neutral'
  },
  rightFootData = {
    underpronation: 0,
    neutral: 39,
    overpronation: 61,
    dominantType: 'overpronation'
  }
}) => {
  const getFootTypeColor = (type: string) => {
    switch (type) {
      case 'underpronation': return 'text-yellow-600';
      case 'neutral': return 'text-green-600';
      case 'overpronation': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getFootTypeLabel = (type: string) => {
    switch (type) {
      case 'underpronation': return 'Underpronation';
      case 'neutral': return 'Neutral';
      case 'overpronation': return 'Overpronation';
      default: return 'Unknown';
    }
  };

  const renderFootAnalysis = (footData: FootPronationData, footName: string) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-semibold text-gray-900">{footName} foot: 
          <span className={`ml-2 ${getFootTypeColor(footData.dominantType)}`}>
            {getFootTypeLabel(footData.dominantType)}
          </span>
        </h4>
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto rounded-full bg-yellow-100 flex items-center justify-center mb-2">
            <span className="text-xl font-bold text-yellow-600">{footData.underpronation}%</span>
          </div>
          <div className="text-xs text-gray-600">Under</div>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 mx-auto rounded-full bg-green-100 flex items-center justify-center mb-2">
            <span className="text-xl font-bold text-green-600">{footData.neutral}%</span>
          </div>
          <div className="text-xs text-gray-600">Neutral</div>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 mx-auto rounded-full bg-red-100 flex items-center justify-center mb-2">
            <span className="text-xl font-bold text-red-600">{footData.overpronation}%</span>
          </div>
          <div className="text-xs text-gray-600">Over</div>
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-xl font-semibold mb-6">Pronation Analysis</h3>
        
        <div className="space-y-8">
          {renderFootAnalysis(leftFootData, 'Left')}
          {renderFootAnalysis(rightFootData, 'Right')}
        </div>

        {/* Foot Diagrams */}
        <div className="mt-8 flex justify-center">
          <div className="grid grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-sm font-medium mb-2">Underpronation</div>
              <div className="text-xs text-gray-500">(right foot)</div>
              <div className="w-12 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mt-2">
                <div className="w-2 h-8 bg-blue-400 rounded transform rotate-12"></div>
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium mb-2">Neutral pronation</div>
              <div className="text-xs text-gray-500">(right foot)</div>
              <div className="w-12 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mt-2">
                <div className="w-2 h-8 bg-blue-400 rounded"></div>
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium mb-2">Overpronation</div>
              <div className="text-xs text-gray-500">(right foot)</div>
              <div className="w-12 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mt-2">
                <div className="w-2 h-8 bg-blue-400 rounded transform -rotate-12"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Analysis Summary</h4>
          <p className="text-sm text-blue-700">
            Left foot shows primarily neutral pronation ({leftFootData.neutral}%), while right foot 
            demonstrates {getFootTypeLabel(rightFootData.dominantType).toLowerCase()} pattern ({rightFootData[rightFootData.dominantType]}%). 
            Consider addressing asymmetry between feet for optimal running efficiency.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PronationAnalysis;
