
import React from 'react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HeaderProps {
  analysisMode: '2D' | '3D';
  onAnalysisModeChange: (mode: '2D' | '3D') => void;
  activityType: 'Running' | 'Cycling';
  onActivityTypeChange: (type: 'Running' | 'Cycling') => void;
  isLocked?: boolean;
}

const Header = ({ 
  analysisMode, 
  onAnalysisModeChange, 
  activityType, 
  onActivityTypeChange,
  isLocked = false
}: HeaderProps) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="max-w-6xl mx-auto flex items-center justify-between">
        {/* Left side - Logo/Title */}
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-sm font-bold">▲</span>
          </div>
          <span className="text-lg font-semibold text-gray-900">Video Analysis Tool</span>
        </div>

        {/* Center - Mode Toggle */}
        <div className="flex items-center gap-4">
          <ToggleGroup 
            type="single" 
            value={analysisMode}
            onValueChange={(value) => {
              if (!isLocked && value) {
                onAnalysisModeChange(value as '2D' | '3D');
              }
            }}
            className={`border border-red-200 rounded-lg p-1 ${
              isLocked ? 'bg-gray-100 opacity-50' : 'bg-red-50'
            }`}
          >
            <ToggleGroupItem 
              value="2D" 
              disabled={isLocked}
              className="data-[state=on]:bg-red-500 data-[state=on]:text-white px-4 py-2 text-sm font-medium disabled:opacity-50"
            >
              2D
            </ToggleGroupItem>
            <ToggleGroupItem 
              value="3D" 
              disabled={isLocked}
              className="data-[state=on]:bg-red-500 data-[state=on]:text-white px-4 py-2 text-sm font-medium disabled:opacity-50"
            >
              3D
            </ToggleGroupItem>
          </ToggleGroup>
          {isLocked && (
            <span className="text-xs text-gray-500">Analysis in progress</span>
          )}
        </div>

        {/* Right side - Activity and Status */}
        <div className="flex items-center gap-4">
          <Tabs 
            value={activityType} 
            onValueChange={(value) => {
              if (!isLocked) {
                onActivityTypeChange(value as 'Running' | 'Cycling');
              }
            }}
          >
            <TabsList className={isLocked ? "bg-gray-100 opacity-50" : "bg-gray-100"}>
              <TabsTrigger value="Running" disabled={isLocked} className="px-4 py-2">Running</TabsTrigger>
              <TabsTrigger value="Cycling" disabled={isLocked} className="px-4 py-2">Cycling</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Activity:</span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
              {activityType}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
