
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface AngleData {
  name: string;
  value: number;
  visible: boolean;
}

interface AnglePanelProps {
  poseData?: any;
  isVisible?: boolean;
  onToggleVisibility?: () => void;
}

const AnglePanel: React.FC<AnglePanelProps> = ({ 
  poseData, 
  isVisible = true, 
  onToggleVisibility 
}) => {
  const [angleToggles, setAngleToggles] = useState({
    leftKnee: true,
    rightKnee: true,
    leftHip: true,
    rightHip: true,
    leftAnkle: true,
    rightAnkle: true,
    torso: true,
  });

  const calculateAngle = (p1: any, vertex: any, p3: any): number => {
    if (!p1 || !vertex || !p3 || p1.score < 0.3 || vertex.score < 0.3 || p3.score < 0.3) {
      return 0;
    }

    const vector1 = { x: p1.x - vertex.x, y: p1.y - vertex.y };
    const vector2 = { x: p3.x - vertex.x, y: p3.y - vertex.y };
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y;
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
    
    const cosAngle = dot / (mag1 * mag2);
    const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
    
    return Math.round((angle * 180) / Math.PI);
  };

  const getAngles = (): AngleData[] => {
    if (!poseData?.pose?.keypoints) {
      return [
        { name: 'Left Knee', value: 0, visible: angleToggles.leftKnee },
        { name: 'Right Knee', value: 0, visible: angleToggles.rightKnee },
        { name: 'Left Hip', value: 0, visible: angleToggles.leftHip },
        { name: 'Right Hip', value: 0, visible: angleToggles.rightHip },
        { name: 'Left Ankle', value: 0, visible: angleToggles.leftAnkle },
        { name: 'Right Ankle', value: 0, visible: angleToggles.rightAnkle },
        { name: 'Torso', value: 0, visible: angleToggles.torso },
      ];
    }

    const keypoints = poseData.pose.keypoints;
    
    return [
      { 
        name: 'Left Knee', 
        value: calculateAngle(keypoints[11], keypoints[13], keypoints[15]), // hip-knee-ankle
        visible: angleToggles.leftKnee 
      },
      { 
        name: 'Right Knee', 
        value: calculateAngle(keypoints[12], keypoints[14], keypoints[16]), 
        visible: angleToggles.rightKnee 
      },
      { 
        name: 'Left Hip', 
        value: calculateAngle(keypoints[5], keypoints[11], keypoints[13]), // shoulder-hip-knee
        visible: angleToggles.leftHip 
      },
      { 
        name: 'Right Hip', 
        value: calculateAngle(keypoints[6], keypoints[12], keypoints[14]), 
        visible: angleToggles.rightHip 
      },
      { 
        name: 'Left Ankle', 
        value: calculateAngle(keypoints[13], keypoints[15], keypoints[19] || keypoints[15]), // knee-ankle-toe
        visible: angleToggles.leftAnkle 
      },
      { 
        name: 'Right Ankle', 
        value: calculateAngle(keypoints[14], keypoints[16], keypoints[20] || keypoints[16]), 
        visible: angleToggles.rightAnkle 
      },
      { 
        name: 'Torso', 
        value: calculateAngle(keypoints[5], keypoints[11], keypoints[13]), 
        visible: angleToggles.torso 
      },
    ];
  };

  const toggleAngle = (angleKey: keyof typeof angleToggles) => {
    setAngleToggles(prev => ({
      ...prev,
      [angleKey]: !prev[angleKey]
    }));
  };

  if (!isVisible) return null;

  const angles = getAngles();

  return (
    <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-20 w-64">
      <Card className="bg-black/80 text-white border-gray-600">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Live Angles</h3>
            <button
              onClick={onToggleVisibility}
              className="text-xs bg-gray-700 hover:bg-gray-600 px-2 py-1 rounded"
            >
              Hide
            </button>
          </div>
          
          <div className="space-y-3">
            {angles.map((angle, index) => {
              const angleKey = Object.keys(angleToggles)[index] as keyof typeof angleToggles;
              return (
                <div key={angle.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={angle.visible}
                      onChange={() => toggleAngle(angleKey)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{angle.name}</span>
                  </div>
                  <div className={`px-2 py-1 rounded text-sm font-mono ${
                    angle.value > 0 ? 'bg-orange-600' : 'bg-gray-600'
                  }`}>
                    {angle.value}°
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnglePanel;
