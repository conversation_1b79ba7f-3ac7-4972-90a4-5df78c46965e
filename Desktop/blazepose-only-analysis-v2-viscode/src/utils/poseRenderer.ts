
export interface PoseKeypoint {
  x: number;
  y: number;
  score: number;
}

export interface ScaledPose {
  keypoints: PoseKeypoint[];
}

export const scaleKeypoints = (
  keypoints: PoseKeypoint[],
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  userHeightMeters?: number
): PoseKeypoint[] => {
  const scaleX = displayWidth / videoWidth;
  const scaleY = displayHeight / videoHeight;
  
  return keypoints.map((kp) => ({
    ...kp,
    x: kp.x * scaleX,
    y: kp.y * scaleY
  }));
};

export const drawPoseConnections = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[]
) => {
  const connections = [
    [5, 6], // shoulders
    [5, 7], [7, 9], // left arm
    [6, 8], [8, 10], // right arm
    [5, 11], [6, 12], // torso
    [11, 12], // hips
    [11, 13], [13, 15], // left leg
    [12, 14], [14, 16] // right leg
  ];

  // RESTORED: Lime green skeleton lines
  ctx.strokeStyle = '#32CD32'; // Lime green
  ctx.lineWidth = 6;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  
  let connectionsDrawn = 0;
  
  connections.forEach(([i, j]) => {
    const kp1 = keypoints[i];
    const kp2 = keypoints[j];
    
    if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
      ctx.beginPath();
      ctx.moveTo(kp1.x, kp1.y);
      ctx.lineTo(kp2.x, kp2.y);
      ctx.stroke();
      connectionsDrawn++;
    }
  });
  
  console.log(`🦴 Drew ${connectionsDrawn} lime green skeleton connections`);
};

export const drawPoseKeypoints = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[]
) => {
  let keypointsDrawn = 0;
  
  keypoints.forEach((keypoint) => {
    if (keypoint.score > 0.3) {
      // RESTORED: Lime green circles with white centers
      const radius = 12;
      
      // Outer lime green circle
      ctx.beginPath();
      ctx.arc(keypoint.x, keypoint.y, radius, 0, 2 * Math.PI);
      ctx.fillStyle = '#32CD32'; // Lime green
      ctx.fill();
      
      // White center circle
      ctx.beginPath();
      ctx.arc(keypoint.x, keypoint.y, radius - 3, 0, 2 * Math.PI);
      ctx.fillStyle = '#FFFFFF';
      ctx.fill();
      
      // Lime green border around white center
      ctx.beginPath();
      ctx.arc(keypoint.x, keypoint.y, radius - 3, 0, 2 * Math.PI);
      ctx.strokeStyle = '#32CD32';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      keypointsDrawn++;
    }
  });
  
  console.log(`🟢 Drew ${keypointsDrawn} lime green keypoints with white centers`);
};

// Helper function to calculate angle between three points
export const calculateAngle = (
  point1: PoseKeypoint,
  point2: PoseKeypoint, // vertex
  point3: PoseKeypoint
): number => {
  const vector1 = { x: point1.x - point2.x, y: point1.y - point2.y };
  const vector2 = { x: point3.x - point2.x, y: point3.y - point2.y };
  
  const dot = vector1.x * vector2.x + vector1.y * vector2.y;
  const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
  const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
  
  const cosAngle = dot / (mag1 * mag2);
  const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  
  return (angle * 180) / Math.PI;
};

// Draw angle arc between three points
const drawAngleArc = (
  ctx: CanvasRenderingContext2D,
  point1: PoseKeypoint,
  vertex: PoseKeypoint,
  point3: PoseKeypoint,
  radius: number = 30
) => {
  // Calculate angles for start and end of arc
  const angle1 = Math.atan2(point1.y - vertex.y, point1.x - vertex.x);
  const angle3 = Math.atan2(point3.y - vertex.y, point3.x - vertex.x);
  
  // Ensure we draw the smaller arc
  let startAngle = angle1;
  let endAngle = angle3;
  
  if (Math.abs(endAngle - startAngle) > Math.PI) {
    if (startAngle > endAngle) {
      endAngle += 2 * Math.PI;
    } else {
      startAngle += 2 * Math.PI;
    }
  }
  
  // RESTORED: Orange arc color
  ctx.beginPath();
  ctx.arc(vertex.x, vertex.y, radius, startAngle, endAngle);
  ctx.strokeStyle = '#FF8C00'; // Orange color for angle arcs
  ctx.lineWidth = 3;
  ctx.stroke();
};

// Draw angle label
const drawAngleLabel = (
  ctx: CanvasRenderingContext2D,
  vertex: PoseKeypoint,
  angle: number,
  offsetX: number = 40,
  offsetY: number = -10
) => {
  const labelX = vertex.x + offsetX;
  const labelY = vertex.y + offsetY;
  
  // RESTORED: Orange background box
  const angleText = `${Math.round(angle)}°`;
  ctx.font = 'bold 14px Arial';
  const textMetrics = ctx.measureText(angleText);
  const padding = 8;
  const boxWidth = textMetrics.width + padding * 2;
  const boxHeight = 20;
  
  ctx.fillStyle = '#FF8C00'; // Orange background
  ctx.fillRect(labelX - padding, labelY - boxHeight + 4, boxWidth, boxHeight);
  
  // Draw white text
  ctx.fillStyle = '#FFFFFF';
  ctx.fillText(angleText, labelX, labelY);
};

// Main function to draw angle annotations
export const drawAngleAnnotations = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[]
) => {
  // Define key angles to measure
  const angleDefinitions = [
    // Knee angles
    { name: 'Left Knee', points: [11, 13, 15], offset: { x: -50, y: -10 } }, // hip-knee-ankle
    { name: 'Right Knee', points: [12, 14, 16], offset: { x: 50, y: -10 } },
    
    // Hip angles  
    { name: 'Left Hip', points: [5, 11, 13], offset: { x: -50, y: 10 } }, // shoulder-hip-knee
    { name: 'Right Hip', points: [6, 12, 14], offset: { x: 50, y: 10 } },
    
    // Ankle angles
    { name: 'Left Ankle', points: [13, 15, 19], offset: { x: -40, y: 20 } }, // knee-ankle-toe (if toe exists)
    { name: 'Right Ankle', points: [14, 16, 20], offset: { x: 40, y: 20 } },
    
    // Additional spine/torso angles
    { name: 'Torso', points: [5, 11, 13], offset: { x: 0, y: -40 } }, // shoulder-hip-knee
  ];
  
  let anglesDrawn = 0;
  
  angleDefinitions.forEach(({ name, points, offset }) => {
    const [p1Idx, vertexIdx, p3Idx] = points;
    const point1 = keypoints[p1Idx];
    const vertex = keypoints[vertexIdx];
    const point3 = keypoints[p3Idx];
    
    // Check if all points exist and have good confidence
    if (point1 && vertex && point3 && 
        point1.score > 0.3 && vertex.score > 0.3 && point3.score > 0.3) {
      
      const angle = calculateAngle(point1, vertex, point3);
      
      // Draw the orange angle arc
      drawAngleArc(ctx, point1, vertex, point3, 25);
      
      // Draw the orange angle label
      drawAngleLabel(ctx, vertex, angle, offset.x, offset.y);
      
      anglesDrawn++;
    }
  });
  
  console.log(`📐 Drew ${anglesDrawn} angle annotations with orange labels and arcs`);
};

export const drawDebugFrame = (
  ctx: CanvasRenderingContext2D,
  frameCount: number
) => {
  // Lime green theme for debug info
  ctx.fillStyle = 'rgba(50, 205, 50, 0.9)'; // Lime green theme
  ctx.fillRect(10, 10, 150, 30);
  ctx.fillStyle = 'white';
  ctx.font = 'bold 14px Arial';
  ctx.fillText(`FRAME ${frameCount}`, 15, 30);
};
