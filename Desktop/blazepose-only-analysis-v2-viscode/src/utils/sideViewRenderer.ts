export interface PoseKeypoint {
  x: number;
  y: number;
  score: number;
}

export const scaleKeypoints = (
  keypoints: PoseKeypoint[],
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  userHeightMeters?: number,
  distanceFeet?: number
): PoseKeypoint[] => {
  const scaleX = displayWidth / videoWidth;
  const scaleY = displayHeight / videoHeight;
  
  // Apply basic scaling first
  const baseScaledKeypoints = keypoints.map((kp) => ({
    ...kp,
    x: kp.x * scaleX,
    y: kp.y * scaleY
  }));
  
  // If height-based scaling parameters are provided, apply additional scaling
  if (userHeightMeters && distanceFeet) {
    // Calculate height-based scale factor
    // At 5 feet, person should appear at a certain size based on their actual height
    const referenceHeightPixels = displayHeight * 0.6; // Assume person takes 60% of frame height at 5 feet
    const expectedHeightPixels = (userHeightMeters * 39.37) * (referenceHeightPixels / 70); // 70 inches as reference
    const distanceScale = 5 / distanceFeet; // Scale based on distance from 5-foot reference
    const heightScale = expectedHeightPixels / referenceHeightPixels;
    
    // Apply combined scaling
    const combinedScale = distanceScale * heightScale;
    
    return baseScaledKeypoints.map((kp) => ({
      ...kp,
      x: kp.x, // Keep X position unchanged
      y: kp.y * combinedScale // Apply height scaling to Y position
    }));
  }
  
  return baseScaledKeypoints;
};

// Determine which way the person is facing based on shoulder positions
const getPersonDirection = (keypoints: PoseKeypoint[]) => {
  const leftShoulder = keypoints[5]; // Left shoulder
  const rightShoulder = keypoints[6]; // Right shoulder
  const nose = keypoints[0];
  
  if (!leftShoulder || !rightShoulder || !nose) return 'unknown';
  
  // Calculate center of shoulders
  const shoulderCenterX = (leftShoulder.x + rightShoulder.x) / 2;
  
  // If nose is to the left of shoulder center, person is facing left
  // If nose is to the right of shoulder center, person is facing right
  return nose.x < shoulderCenterX ? 'left' : 'right';
};

// Determine if a limb is on the far side (less visible in side view)
const isFarSideLimb = (keypointIndex: number, direction: string) => {
  // For left-facing person: right limbs are far side
  // For right-facing person: left limbs are far side
  const rightSideLimbs = [6, 8, 10, 12, 14, 16]; // right shoulder, elbow, wrist, hip, knee, ankle
  const leftSideLimbs = [5, 7, 9, 11, 13, 15]; // left shoulder, elbow, wrist, hip, knee, ankle
  
  if (direction === 'left') {
    return rightSideLimbs.includes(keypointIndex);
  } else if (direction === 'right') {
    return leftSideLimbs.includes(keypointIndex);
  }
  return false;
};

export const drawSideViewPoseConnections = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  overlayStyle: string = 'Medical'
) => {
  const direction = getPersonDirection(keypoints);
  
  // Define connections based on overlay style
  let connections = [];
  
  if (overlayStyle === 'Medical') {
    // Remove face connections, add neck-to-torso connection
    connections = [
      [0, 5], [0, 6], // neck to shoulders (to show neck angle reference)
      [5, 6], // shoulders
      [5, 7], [7, 9], // left arm
      [6, 8], [8, 10], // right arm
      [5, 11], [6, 12], // torso
      [11, 12], // hips
      [11, 13], [13, 15], // left leg
      [12, 14], [14, 16] // right leg
    ];
  } else {
    // Dynamic and Minimal - no head connections
    connections = [
      [5, 6], // shoulders
      [5, 7], [7, 9], // left arm
      [6, 8], [8, 10], // right arm
      [5, 11], [6, 12], // torso
      [11, 12], // hips
      [11, 13], [13, 15], // left leg
      [12, 14], [14, 16] // right leg
    ];
  }

  ctx.lineWidth = 5;
  ctx.lineCap = 'round';
  ctx.lineJoin = 'round';
  
  let connectionsDrawn = 0;
  
  connections.forEach(([i, j]) => {
    const kp1 = keypoints[i];
    const kp2 = keypoints[j];
    
    if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
      // Determine if this connection involves far side limbs
      const isFarSideConnection = isFarSideLimb(i, direction) || isFarSideLimb(j, direction);
      
      // Set color and opacity based on side
      if (isFarSideConnection) {
        ctx.strokeStyle = 'rgba(50, 205, 50, 0.4)'; // Far side: reduced opacity lime green
      } else {
        ctx.strokeStyle = '#32CD32'; // Near side: full opacity lime green
      }
      
      ctx.beginPath();
      ctx.moveTo(kp1.x, kp1.y);
      ctx.lineTo(kp2.x, kp2.y);
      ctx.stroke();
      connectionsDrawn++;
    }
  });
  
  console.log(`🦴 Drew ${connectionsDrawn} skeleton connections with depth (${overlayStyle} style, facing ${direction})`);
};

export const drawSideViewPoseKeypoints = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  overlayStyle: string = 'Medical'
) => {
  const direction = getPersonDirection(keypoints);
  let keypointsDrawn = 0;
  
  keypoints.forEach((keypoint, index) => {
    // For Medical style: show nose (0) and ears (3, 4), but only close side ear
    // For other styles: only show nose (0)
    if (overlayStyle === 'Medical') {
      // Skip eyes (1, 2) but keep nose (0) and handle ears (3, 4)
      if (index === 1 || index === 2) {
        return;
      }
      
      // For ears, only show the close side ear
      if (index === 3 || index === 4) { // left ear = 3, right ear = 4
        const isLeftEar = index === 3;
        const isRightEar = index === 4;
        
        // Show close side ear only
        if ((direction === 'left' && isRightEar) || (direction === 'right' && isLeftEar)) {
          return; // Skip far side ear
        }
      }
    } else {
      // For Dynamic and Minimal styles: skip all face keypoints except nose
      if (index >= 1 && index <= 4) {
        return;
      }
    }
    
    if (keypoint.score > 0.3) {
      const radius = 8;
      const isFarSide = isFarSideLimb(index, direction);
      
      // Special handling for feet - make them more prominent
      const isFootKeypoint = index === 15 || index === 16; // ankle keypoints
      const footRadius = isFootKeypoint ? 12 : radius;
      
      // Set colors based on side depth
      const outerColor = isFarSide ? 'rgba(50, 205, 50, 0.4)' : '#32CD32';
      const innerColor = isFarSide ? 'rgba(255, 255, 255, 0.6)' : '#FFFFFF';
      const borderColor = isFarSide ? 'rgba(50, 205, 50, 0.4)' : '#32CD32';
      
      // Enhanced foot styling
      if (isFootKeypoint) {
        // Outer circle for feet - larger and more prominent
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, footRadius, 0, 2 * Math.PI);
        ctx.fillStyle = outerColor;
        ctx.fill();
        
        // White center circle for feet
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, footRadius - 4, 0, 2 * Math.PI);
        ctx.fillStyle = innerColor;
        ctx.fill();
        
        // Thicker border for feet
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, footRadius - 4, 0, 2 * Math.PI);
        ctx.strokeStyle = borderColor;
        ctx.lineWidth = 3;
        ctx.stroke();
      } else {
        // Regular keypoint styling
        // Outer circle
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, radius, 0, 2 * Math.PI);
        ctx.fillStyle = outerColor;
        ctx.fill();
        
        // White center circle
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, radius - 3, 0, 2 * Math.PI);
        ctx.fillStyle = innerColor;
        ctx.fill();
        
        // Border around white center
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, radius - 3, 0, 2 * Math.PI);
        ctx.strokeStyle = borderColor;
        ctx.lineWidth = 2;
        ctx.stroke();
      }
      
      keypointsDrawn++;
    }
  });
  
  console.log(`🔵 Drew ${keypointsDrawn} keypoints with depth and enhanced feet (${overlayStyle} style, facing ${direction})`);
};

// Helper function to calculate angle between three points
export const calculateAngle = (
  point1: PoseKeypoint,
  point2: PoseKeypoint, // vertex
  point3: PoseKeypoint
): number => {
  const vector1 = { x: point1.x - point2.x, y: point1.y - point2.y };
  const vector2 = { x: point3.x - point2.x, y: point3.y - point2.y };
  
  const dot = vector1.x * vector2.x + vector1.y * vector2.y;
  const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
  const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
  
  const cosAngle = dot / (mag1 * mag2);
  const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  
  return (angle * 180) / Math.PI;
};

// Draw solid angle arc between three points with opacity support
const drawSolidAngleArc = (
  ctx: CanvasRenderingContext2D,
  point1: PoseKeypoint,
  vertex: PoseKeypoint,
  point3: PoseKeypoint,
  radius: number = 30,
  isFarSide: boolean = false
) => {
  // Calculate angles for start and end of arc
  const angle1 = Math.atan2(point1.y - vertex.y, point1.x - vertex.x);
  const angle3 = Math.atan2(point3.y - vertex.y, point3.x - vertex.x);
  
  // Ensure we draw the smaller arc
  let startAngle = angle1;
  let endAngle = angle3;
  
  if (Math.abs(endAngle - startAngle) > Math.PI) {
    if (startAngle > endAngle) {
      endAngle += 2 * Math.PI;
    } else {
      startAngle += 2 * Math.PI;
    }
  }
  
  // Set opacity based on far side
  const fillOpacity = isFarSide ? 0.12 : 0.3; // Reduced opacity for far side
  const strokeOpacity = isFarSide ? 0.4 : 1.0; // Reduced opacity for far side
  
  // Draw solid arc (filled sector)
  ctx.beginPath();
  ctx.moveTo(vertex.x, vertex.y);
  ctx.arc(vertex.x, vertex.y, radius, startAngle, endAngle);
  ctx.closePath();
  ctx.fillStyle = `rgba(255, 140, 0, ${fillOpacity})`; // Semi-transparent orange with varying opacity
  ctx.fill();
  
  // Draw arc outline
  ctx.beginPath();
  ctx.arc(vertex.x, vertex.y, radius, startAngle, endAngle);
  ctx.strokeStyle = `rgba(255, 140, 0, ${strokeOpacity})`; // Orange color with varying opacity
  ctx.lineWidth = 2;
  ctx.stroke();
};

// Draw angle label with opacity support
const drawAngleLabel = (
  ctx: CanvasRenderingContext2D,
  vertex: PoseKeypoint,
  angle: number,
  offsetX: number = 40,
  offsetY: number = -10,
  isFarSide: boolean = false
) => {
  const labelX = vertex.x + offsetX;
  const labelY = vertex.y + offsetY;
  
  // Set opacity based on far side
  const backgroundOpacity = isFarSide ? 0.4 : 1.0; // Reduced opacity for far side
  const textOpacity = isFarSide ? 0.6 : 1.0; // Reduced opacity for far side
  
  // Draw orange background box
  const angleText = `${Math.round(angle)}°`;
  ctx.font = 'bold 14px Arial';
  const textMetrics = ctx.measureText(angleText);
  const padding = 8;
  const boxWidth = textMetrics.width + padding * 2;
  const boxHeight = 20;
  
  ctx.fillStyle = `rgba(255, 140, 0, ${backgroundOpacity})`; // Orange background with varying opacity
  ctx.fillRect(labelX - padding, labelY - boxHeight + 4, boxWidth, boxHeight);
  
  // Draw white text
  ctx.fillStyle = `rgba(255, 255, 255, ${textOpacity})`; // White text with varying opacity
  ctx.fillText(angleText, labelX, labelY);
};

// Main function to draw angle annotations for side view
export const drawSideViewAngleAnnotations = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  overlayStyle: string = 'Medical'
) => {
  // Skip angle annotations for Minimal style
  if (overlayStyle === 'Minimal') {
    console.log('📐 Skipped angle annotations for Minimal style');
    return;
  }

  const direction = getPersonDirection(keypoints);

  // Define key angles to measure for side view
  const angleDefinitions = [
    // Knee angles
    { name: 'Left Knee', points: [11, 13, 15], offset: { x: -50, y: -10 } }, // hip-knee-ankle
    { name: 'Right Knee', points: [12, 14, 16], offset: { x: 50, y: -10 } },
    
    // Hip angles - only show close side hip for Medical style
    { name: 'Left Hip', points: [5, 11, 13], offset: { x: -50, y: 10 }, skipIfFarSide: overlayStyle === 'Medical' }, // shoulder-hip-knee
    { name: 'Right Hip', points: [6, 12, 14], offset: { x: 50, y: 10 }, skipIfFarSide: overlayStyle === 'Medical' },
    
    // Elbow angles - NEW: forearm to upper arm
    { name: 'Left Elbow', points: [5, 7, 9], offset: { x: -40, y: -30 } }, // shoulder-elbow-wrist
    { name: 'Right Elbow', points: [6, 8, 10], offset: { x: 40, y: -30 } }, // shoulder-elbow-wrist
    
    // Ankle angles
    { name: 'Left Ankle', points: [13, 15, 19], offset: { x: -40, y: 20 } }, // knee-ankle-toe (if toe exists)
    { name: 'Right Ankle', points: [14, 16, 20], offset: { x: 40, y: 20 } },
    
    // Additional spine/torso angles
    { name: 'Torso', points: [5, 11, 13], offset: { x: 0, y: -40 } }, // shoulder-hip-knee
  ];
  
  // Add ear-to-nose angle for Medical style only
  if (overlayStyle === 'Medical') {
    // Only add close side ear angle
    if (direction === 'left') {
      // Show left ear to nose angle (close side when facing left)
      angleDefinitions.push(
        { name: 'Ear Angle', points: [3, 0, 5], offset: { x: -30, y: -50 } } // left_ear-nose-left_shoulder
      );
    } else if (direction === 'right') {
      // Show right ear to nose angle (close side when facing right)
      angleDefinitions.push(
        { name: 'Ear Angle', points: [4, 0, 6], offset: { x: 30, y: -50 } } // right_ear-nose-right_shoulder
      );
    }
  }
  
  let anglesDrawn = 0;
  
  angleDefinitions.forEach(({ name, points, offset, skipIfFarSide }) => {
    const [p1Idx, vertexIdx, p3Idx] = points;
    const point1 = keypoints[p1Idx];
    const vertex = keypoints[vertexIdx];
    const point3 = keypoints[p3Idx];
    
    // Check if all points exist and have good confidence
    if (point1 && vertex && point3 && 
        point1.score > 0.3 && vertex.score > 0.3 && point3.score > 0.3) {
      
      // Determine if this angle involves far side limbs
      const isFarSideAngle = isFarSideLimb(p1Idx, direction) || 
                            isFarSideLimb(vertexIdx, direction) || 
                            isFarSideLimb(p3Idx, direction);
      
      // Skip far side hip angles for Medical style
      if (skipIfFarSide && isFarSideAngle) {
        return;
      }
      
      const angle = calculateAngle(point1, vertex, point3);
      
      // Draw the solid angle arc with opacity
      drawSolidAngleArc(ctx, point1, vertex, point3, 25, isFarSideAngle);
      
      // Draw the angle label with opacity
      drawAngleLabel(ctx, vertex, angle, offset.x, offset.y, isFarSideAngle);
      
      anglesDrawn++;
    }
  });
  
  console.log(`📐 Drew ${anglesDrawn} angle annotations with enhanced Medical style features (${overlayStyle} style)`);
};

// Draw direction indicator to show which way the person is facing
export const drawDirectionIndicator = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  canvasWidth: number
) => {
  const direction = getPersonDirection(keypoints);
  
  if (direction === 'unknown') return;
  
  // Draw direction indicator in top-left corner
  const indicatorX = 15;
  const indicatorY = 60;
  
  // Background box
  ctx.fillStyle = 'rgba(50, 205, 50, 0.9)';
  ctx.fillRect(indicatorX, indicatorY, 120, 25);
  
  // Direction text
  ctx.fillStyle = 'white';
  ctx.font = 'bold 12px Arial';
  const text = `FACING ${direction.toUpperCase()}`;
  ctx.fillText(text, indicatorX + 5, indicatorY + 17);
  
  // Arrow indicator
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = 2;
  ctx.beginPath();
  
  if (direction === 'left') {
    // Left-pointing arrow
    ctx.moveTo(indicatorX + 105, indicatorY + 8);
    ctx.lineTo(indicatorX + 95, indicatorY + 12);
    ctx.lineTo(indicatorX + 105, indicatorY + 16);
  } else {
    // Right-pointing arrow
    ctx.moveTo(indicatorX + 95, indicatorY + 8);
    ctx.lineTo(indicatorX + 105, indicatorY + 12);
    ctx.lineTo(indicatorX + 95, indicatorY + 16);
  }
  
  ctx.stroke();
  
  console.log(`🧭 Drew direction indicator: facing ${direction}`);
};

export const drawDebugFrame = (
  ctx: CanvasRenderingContext2D,
  frameCount: number
) => {
  // Side view debug info with lime green theme
  ctx.fillStyle = 'rgba(50, 205, 50, 0.9)'; // Lime green theme
  ctx.fillRect(10, 10, 150, 30);
  ctx.fillStyle = 'white';
  ctx.font = 'bold 14px Arial';
  ctx.fillText(`SIDE ${frameCount}`, 15, 30);
};
