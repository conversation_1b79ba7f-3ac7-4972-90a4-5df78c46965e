
export interface BlazePoseKeypoint {
  x: number;
  y: number;
  z?: number; // 3D coordinate when available
  score: number;
  name?: string;
}

export interface BlazePoseData {
  frameNumber: number;
  timestamp: number;
  keypoints: BlazePoseKeypoint[];
  modelQuality: 'Full' | 'Heavy';
  pose3D?: any; // Extended 3D pose data
}

// BlazePose keypoint indices and names
export const BLAZEPOSE_KEYPOINTS = {
  NOSE: 0,
  LEFT_EYE_INNER: 1,
  LEFT_EYE: 2,
  LEFT_EYE_OUTER: 3,
  RIGHT_EYE_INNER: 4,
  RIGHT_EYE: 5,
  RIGHT_EYE_OUTER: 6,
  LEFT_EAR: 7,
  RIGHT_EAR: 8,
  MOUTH_LEFT: 9,
  MOUTH_RIGHT: 10,
  LEFT_SHOULDER: 11,
  RIGHT_SHOULDER: 12,
  LEFT_ELBOW: 13,
  RIGHT_ELBOW: 14,
  LEFT_WRIST: 15,
  RIGHT_WRIST: 16,
  LEFT_PINKY: 17,
  RIGHT_PINKY: 18,
  LEFT_INDEX: 19,
  RIGHT_INDEX: 20,
  LEFT_THUMB: 21,
  RIGHT_THUMB: 22,
  LEFT_HIP: 23,
  RIGHT_HIP: 24,
  LEFT_KNEE: 25,
  RIGHT_KNEE: 26,
  LEFT_ANKLE: 27,
  RIGHT_ANKLE: 28,
  LEFT_HEEL: 29,
  RIGHT_HEEL: 30,
  LEFT_FOOT_INDEX: 31,
  RIGHT_FOOT_INDEX: 32,
};

export const processBlazePoseData = (
  poses: any[], 
  frameCount: number,
  modelQuality: 'Full' | 'Heavy'
): BlazePoseData | null => {
  if (poses.length === 0) return null;

  const pose = poses[0];
  
  return {
    frameNumber: frameCount,
    timestamp: Date.now(),
    modelQuality,
    keypoints: pose.keypoints.map((kp: any, index: number) => ({
      x: kp.x,
      y: kp.y,
      z: kp.z || 0, // BlazePose provides z-coordinate
      score: kp.score ?? 0,
      name: Object.keys(BLAZEPOSE_KEYPOINTS)[index]
    })),
    pose3D: pose.keypoints3D || null // Full 3D pose data when available
  };
};

// Calculate 3D angles between joints
export const calculate3DAngle = (
  point1: BlazePoseKeypoint,
  point2: BlazePoseKeypoint,
  point3: BlazePoseKeypoint
): number => {
  const vector1 = {
    x: point1.x - point2.x,
    y: point1.y - point2.y,
    z: (point1.z || 0) - (point2.z || 0)
  };
  
  const vector2 = {
    x: point3.x - point2.x,
    y: point3.y - point2.y,
    z: (point3.z || 0) - (point2.z || 0)
  };
  
  const dot = vector1.x * vector2.x + vector1.y * vector2.y + vector1.z * vector2.z;
  const mag1 = Math.sqrt(vector1.x ** 2 + vector1.y ** 2 + vector1.z ** 2);
  const mag2 = Math.sqrt(vector2.x ** 2 + vector2.y ** 2 + vector2.z ** 2);
  
  if (mag1 === 0 || mag2 === 0) return 0;
  
  const cosAngle = dot / (mag1 * mag2);
  return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI);
};

// Future: 3D biomechanics calculations will be added here
export const calculate3DRunningMetrics = (poseData: BlazePoseData) => {
  // Future implementation will include:
  // - 3D stride analysis
  // - Full body joint angles
  // - Center of mass calculations
  // - 3D ground reaction forces estimation
  // - Advanced gait analysis
  return {};
};
