/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

// Re-export all types from blazepose_tfjs and blazepose_mediapipe
export * from './blazepose_tfjs/types';
export * from './blazepose_mediapipe/types';
export * from './shared/calculators/interfaces/common_interfaces';

// Additional types for pose detection
export interface Pose {
  keypoints: Array<{x: number; y: number; z?: number; score?: number; name?: string}>;
  keypoints3D?: Array<{x: number; y: number; z?: number; score?: number; name?: string}>;
  score?: number;
  segmentation?: any;
  id?: number;
  box?: {
    xMin: number;
    yMin: number;
    xMax: number;
    yMax: number;
    width: number;
    height: number;
  };
}

export type PoseDetectorInput = HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | ImageData | import('@tensorflow/tfjs-core').Tensor3D;