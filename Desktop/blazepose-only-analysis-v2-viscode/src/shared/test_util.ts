/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

export const KARMA_SERVER = 'http://localhost:9876';

export function loadImage(src: string, width: number, height: number): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
    img.width = width;
    img.height = height;
  });
}

export function loadVideo(
  src: string, 
  fps: number, 
  callback: (video: HTMLVideoElement, timestamp: number) => Promise<any>,
  expected: any,
  adjacentPairs: any,
  simulatedInterval: number
): Promise<void> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.src = src;
    video.onloadeddata = () => resolve();
    video.onerror = reject;
  });
}

export function getXYPerFrame(result: any): number[][][] {
  return result.map((frame: any) => 
    frame.map((keypoint: any) => [keypoint.x, keypoint.y])
  );
}

export function imageToBooleanMask(
  imageData: Uint8ClampedArray, 
  redThreshold: number, 
  greenThreshold: number, 
  blueThreshold: number
): boolean[] {
  const mask: boolean[] = [];
  for (let i = 0; i < imageData.length; i += 4) {
    const red = imageData[i];
    const green = imageData[i + 1];
    const blue = imageData[i + 2];
    mask.push(red > redThreshold || green > greenThreshold || blue > blueThreshold);
  }
  return mask;
}

export function segmentationIOU(mask1: boolean[], mask2: boolean[]): number {
  let intersection = 0;
  let union = 0;
  
  for (let i = 0; i < Math.min(mask1.length, mask2.length); i++) {
    if (mask1[i] && mask2[i]) intersection++;
    if (mask1[i] || mask2[i]) union++;
  }
  
  return union === 0 ? 1 : intersection / union;
}