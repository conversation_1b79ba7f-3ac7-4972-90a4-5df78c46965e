
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from '../calculators/interfaces/common_interfaces';

export interface KeypointsSmoothingConfig {
  frequency: number;
  minCutOff: number;
  beta: number;
  derivateCutOff: number;
  thresholdCutOff: number;
  thresholdBeta: number;
  disableValueScaling: boolean;
  velocitySmoothing?: number;
  accelerationDamping?: number;
}

/**
 * PHASE 4: Enhanced One Euro Filter for smoothing keypoint positions with velocity and acceleration tracking.
 */
export class KeypointsSmoothingFilter {
  private readonly config: KeypointsSmoothingConfig;
  private previousValues?: Keypoint[];
  private previousTimestamp?: number;
  private velocities?: { x: number; y: number; z?: number }[];
  private previousVelocities?: { x: number; y: number; z?: number }[];

  constructor(config: KeypointsSmoothingConfig) {
    this.config = {
      ...config,
      velocitySmoothing: config.velocitySmoothing || 0.1,
      accelerationDamping: config.accelerationDamping || 0.05
    };
  }

  apply(
      keypoints: Keypoint[], 
      timestamp?: number, 
      imageSize?: any, 
      normalized?: boolean, 
      objectScale?: any): Keypoint[] {
    console.log('🔧 PHASE 4 KEYPOINTS SMOOTHING: Applying enhanced temporal smoothing with velocity tracking');
    console.log('🔧 PHASE 4 KEYPOINTS SMOOTHING: Input keypoints count:', keypoints.length);

    const currentTimestamp = Date.now();

    if (!this.previousValues || !this.previousTimestamp) {
      this.previousValues = [...keypoints];
      this.previousTimestamp = currentTimestamp;
      this.velocities = keypoints.map(() => ({ x: 0, y: 0, z: 0 }));
      this.previousVelocities = keypoints.map(() => ({ x: 0, y: 0, z: 0 }));
      return keypoints;
    }

    const deltaTime = Math.max(0.001, (currentTimestamp - this.previousTimestamp) / 1000); // Prevent division by zero
    
    const smoothedKeypoints: Keypoint[] = keypoints.map((keypoint, index) => {
      const prevKeypoint = this.previousValues![index];
      
      if (!prevKeypoint) {
        return keypoint;
      }

      // PHASE 4: Calculate current velocity
      const currentVelocity = {
        x: (keypoint.x - prevKeypoint.x) / deltaTime,
        y: (keypoint.y - prevKeypoint.y) / deltaTime,
        z: keypoint.z !== undefined && prevKeypoint.z !== undefined ? 
          (keypoint.z - prevKeypoint.z) / deltaTime : 0
      };

      // PHASE 4: Smooth velocity to reduce jitter
      const prevVel = this.velocities![index] || { x: 0, y: 0, z: 0 };
      const smoothedVelocity = {
        x: this.config.velocitySmoothing! * currentVelocity.x + (1 - this.config.velocitySmoothing!) * prevVel.x,
        y: this.config.velocitySmoothing! * currentVelocity.y + (1 - this.config.velocitySmoothing!) * prevVel.y,
        z: this.config.velocitySmoothing! * currentVelocity.z + (1 - this.config.velocitySmoothing!) * prevVel.z
      };

      this.velocities![index] = smoothedVelocity;

      // PHASE 4: Calculate acceleration for damping
      const prevPrevVel = this.previousVelocities![index] || { x: 0, y: 0, z: 0 };
      const acceleration = {
        x: Math.abs(smoothedVelocity.x - prevPrevVel.x),
        y: Math.abs(smoothedVelocity.y - prevPrevVel.y),
        z: Math.abs(smoothedVelocity.z - prevPrevVel.z)
      };

      // PHASE 4: Adaptive alpha based on velocity and confidence
      const velocityMagnitude = Math.sqrt(smoothedVelocity.x ** 2 + smoothedVelocity.y ** 2);
      const accelerationMagnitude = Math.sqrt(acceleration.x ** 2 + acceleration.y ** 2);
      
      // Base alpha from one euro filter
      let alpha = this.config.minCutOff / (this.config.minCutOff + deltaTime);
      
      // PHASE 4: Adjust alpha based on motion characteristics
      if (velocityMagnitude > 100) { // Fast motion
        alpha = Math.min(0.8, alpha * 1.5); // Less smoothing for fast motion
      } else if (velocityMagnitude < 10) { // Slow motion
        alpha = Math.max(0.1, alpha * 0.7); // More smoothing for slow motion
      }
      
      // PHASE 4: Apply acceleration damping
      if (accelerationMagnitude > 50) { // High acceleration/jitter
        alpha = Math.max(0.05, alpha * (1 - this.config.accelerationDamping!));
      }

      // PHASE 4: Confidence-based smoothing adjustment
      const confidence = keypoint.score || 0;
      if (confidence < 0.3) {
        alpha = Math.max(0.05, alpha * 0.5); // Strong smoothing for low confidence
      }

      const smoothedX = alpha * keypoint.x + (1 - alpha) * prevKeypoint.x;
      const smoothedY = alpha * keypoint.y + (1 - alpha) * prevKeypoint.y;
      const smoothedZ = keypoint.z !== undefined && prevKeypoint.z !== undefined ? 
        alpha * keypoint.z + (1 - alpha) * prevKeypoint.z : keypoint.z;

      // Store previous velocity for acceleration calculation
      this.previousVelocities![index] = { ...smoothedVelocity };

      return {
        x: smoothedX,
        y: smoothedY,
        z: smoothedZ,
        score: keypoint.score,
        name: keypoint.name
      };
    });

    this.previousValues = [...smoothedKeypoints];
    this.previousTimestamp = currentTimestamp;

    console.log(`🔧 PHASE 4 KEYPOINTS SMOOTHING: Successfully smoothed ${smoothedKeypoints.length} keypoints with velocity and acceleration tracking`);
    return smoothedKeypoints;
  }

  reset(): void {
    console.log('🔧 PHASE 4 KEYPOINTS SMOOTHING: Resetting enhanced filter state');
    this.previousValues = undefined;
    this.previousTimestamp = undefined;
    this.velocities = undefined;
    this.previousVelocities = undefined;
  }
}
