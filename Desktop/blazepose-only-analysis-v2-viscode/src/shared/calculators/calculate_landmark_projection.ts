
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * Projects landmarks from ROI coordinates back to the original image coordinates.
 * This is critical for displaying landmarks in the correct position on the full image.
 */
export function calculateLandmarkProjection(
    landmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 LANDMARK PROJECTION: Projecting landmarks from ROI to image coordinates');
  console.log('🔧 LANDMARK PROJECTION: ROI rect:', rect);
  console.log('🔧 LANDMARK PROJECTION: Input landmarks count:', landmarks.length);

  const projectedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // PHASE 6B: NaN-safe coordinate handling
    let x = landmark.x;
    let y = landmark.y;
    
    // PHASE 6B: Validate input coordinates
    if (isNaN(x) || !isFinite(x)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN X in landmark projection ${index}`);
      x = 0.5;
    }
    if (isNaN(y) || !isFinite(y)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN Y in landmark projection ${index}`);
      y = 0.5;
    }
    
    // Handle rotation if present
    if (rect.rotation && rect.rotation !== 0 && isFinite(rect.rotation)) {
      const centerX = 0.5;
      const centerY = 0.5;
      const cos = Math.cos(rect.rotation);
      const sin = Math.sin(rect.rotation);
      
      const translatedX = x - centerX;
      const translatedY = y - centerY;
      
      const rotatedX = translatedX * cos - translatedY * sin + centerX;
      const rotatedY = translatedX * sin + translatedY * cos + centerY;
      
      // PHASE 6B: Validate rotated coordinates
      x = isFinite(rotatedX) ? rotatedX : x;
      y = isFinite(rotatedY) ? rotatedY : y;
    }

    // PHASE 6B: Safe scaling and translation
    let projectedX = x * rect.width + rect.xCenter - rect.width / 2;
    let projectedY = y * rect.height + rect.yCenter - rect.height / 2;
    
    // PHASE 6B: Validate projected coordinates
    if (isNaN(projectedX) || !isFinite(projectedX)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN projected X at landmark ${index}`);
      projectedX = rect.xCenter;
    }
    if (isNaN(projectedY) || !isFinite(projectedY)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN projected Y at landmark ${index}`);
      projectedY = rect.yCenter;
    }

    const projectedLandmark: Keypoint = {
      x: projectedX,
      y: projectedY,
      score: landmark.score,
      name: landmark.name
    };

    // Preserve z-coordinate if it exists
    if (landmark.z !== undefined) {
      let z = landmark.z;
      if (isNaN(z) || !isFinite(z)) {
        z = 0;
      }
      projectedLandmark.z = z;
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 PHASE 6B: Landmark ${index} projection:`, {
        roi: { x: landmark.x?.toFixed(4), y: landmark.y?.toFixed(4) },
        projected: { x: projectedX.toFixed(4), y: projectedY.toFixed(4) },
        score: landmark.score?.toFixed(4)
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 LANDMARK PROJECTION: Successfully projected ${projectedLandmarks.length} landmarks`);
  return projectedLandmarks;
}
