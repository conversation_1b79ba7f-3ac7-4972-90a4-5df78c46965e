/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Detection } from './interfaces/common_interfaces';

export interface NonMaxSuppressionConfig {
  minScoreThreshold: number;
  minSuppressionThreshold: number;
  overlapType?: 'intersection-over-union' | 'jaccard';
}

/**
 * Performs non-maximum suppression on detections.
 * Removes overlapping detections that have lower confidence scores.
 */
export function nonMaxSuppression(
    detections: Detection[],
    config: NonMaxSuppressionConfig): Detection[] {
  
  // Add protection against infinite loops and excessive processing
  if (!detections || detections.length === 0) {
    return [];
  }

  if (!config || typeof config.minScoreThreshold !== 'number' || 
      typeof config.minSuppressionThreshold !== 'number') {
    console.error('🔧 NON_MAX_SUPPRESSION: Invalid configuration');
    return []; // Return empty array if config is invalid
  }

  // CRITICAL: Limit detections to prevent infinite loops
  const MAX_DETECTIONS = 100;
  let processDetections = detections;
  
  if (detections.length > MAX_DETECTIONS) {
    // Take only the top scoring detections
    processDetections = detections
      .filter(d => d && typeof d.score === 'number')
      .sort((a, b) => b.score - a.score)
      .slice(0, MAX_DETECTIONS);
    // Limited to MAX_DETECTIONS
  }

  // Filter valid detections with minimal logging
  const validDetections = processDetections.filter(detection => {
    return detection && 
           typeof detection.score === 'number' && 
           !isNaN(detection.score) &&
           (detection.boundingBox || detection.relativeBoundingBox);
  });

  if (validDetections.length === 0) {
    return [];
  }

  // Filter by score threshold
  const filteredDetections = validDetections.filter(detection => 
    detection.score >= config.minScoreThreshold
  );

  if (filteredDetections.length === 0) {
    return [];
  }

  // Sort detections by score in descending order
  const sortedDetections = filteredDetections.sort((a, b) => b.score - a.score);

  const selectedDetections: Detection[] = [];
  const suppressedIndices = new Set<number>();

  for (let i = 0; i < sortedDetections.length; i++) {
    if (suppressedIndices.has(i)) {
      continue;
    }

    const currentDetection = sortedDetections[i];
    selectedDetections.push(currentDetection);

    // Remove logging from inner loop to prevent console spam
    // Suppress overlapping detections (processing in batches to avoid infinite loops)
    const MAX_COMPARISONS = 50; // Limit comparisons per detection
    let comparisons = 0;
    
    for (let j = i + 1; j < sortedDetections.length && comparisons < MAX_COMPARISONS; j++) {
      if (suppressedIndices.has(j)) {
        continue;
      }

      const otherDetection = sortedDetections[j];
      comparisons++;
      
      try {
        const overlap = calculateOverlap(currentDetection, otherDetection, config.overlapType || 'intersection-over-union');

        if (overlap > config.minSuppressionThreshold) {
          suppressedIndices.add(j);
        }
      } catch (error) {
        // Continue processing other detections instead of failing
        continue;
      }
    }
  }

  console.log(`🔧 NMS: Selected ${selectedDetections.length} detections`);
  return selectedDetections;
}

/**
 * Calculates overlap between two detections.
 * FIXED: Handles both normalized and absolute coordinate systems consistently.
 */
function calculateOverlap(
    detection1: Detection,
    detection2: Detection,
    overlapType: 'intersection-over-union' | 'jaccard'): number {
  
  // Silent overlap calculation to prevent console spam
  let box1, box2;
  
  // Get bounding box for detection1 - convert Rect to BoundingBox format if needed
  if (detection1.relativeBoundingBox) {
    const rect1 = detection1.relativeBoundingBox;
    // Convert Rect format {xCenter, yCenter, width, height} to BoundingBox format
    const xMin = Math.max(0, Math.min(1, rect1.xCenter - rect1.width / 2));
    const yMin = Math.max(0, Math.min(1, rect1.yCenter - rect1.height / 2));
    const xMax = Math.max(0, Math.min(1, rect1.xCenter + rect1.width / 2));
    const yMax = Math.max(0, Math.min(1, rect1.yCenter + rect1.height / 2));
    
    box1 = {
      xMin: xMin,
      yMin: yMin,
      xMax: Math.max(xMin + 0.001, xMax), // Ensure xMax > xMin
      yMax: Math.max(yMin + 0.001, yMax), // Ensure yMax > yMin
      width: Math.max(0.001, xMax - xMin),
      height: Math.max(0.001, yMax - yMin)
    };
  } else if (detection1.boundingBox) {
    box1 = detection1.boundingBox;
  } else {
    return 0.0;
  }
  
  // Get bounding box for detection2 - convert Rect to BoundingBox format if needed
  if (detection2.relativeBoundingBox) {
    const rect2 = detection2.relativeBoundingBox;
    // Convert Rect format {xCenter, yCenter, width, height} to BoundingBox format
    const xMin = Math.max(0, Math.min(1, rect2.xCenter - rect2.width / 2));
    const yMin = Math.max(0, Math.min(1, rect2.yCenter - rect2.height / 2));
    const xMax = Math.max(0, Math.min(1, rect2.xCenter + rect2.width / 2));
    const yMax = Math.max(0, Math.min(1, rect2.yCenter + rect2.height / 2));
    
    box2 = {
      xMin: xMin,
      yMin: yMin,
      xMax: Math.max(xMin + 0.001, xMax), // Ensure xMax > xMin
      yMax: Math.max(yMin + 0.001, yMax), // Ensure yMax > yMin
      width: Math.max(0.001, xMax - xMin),
      height: Math.max(0.001, yMax - yMin)
    };
  } else if (detection2.boundingBox) {
    box2 = detection2.boundingBox;
  } else {
    return 0.0;
  }

  // Validate bounding box coordinates
  if (!isValidBoundingBox(box1) || !isValidBoundingBox(box2)) {
    return 0.0;
  }

  // Calculate intersection area
  const xMin = Math.max(box1.xMin, box2.xMin);
  const yMin = Math.max(box1.yMin, box2.yMin);
  const xMax = Math.min(box1.xMax, box2.xMax);
  const yMax = Math.min(box1.yMax, box2.yMax);

  if (xMin >= xMax || yMin >= yMax) {
    return 0.0;
  }

  const intersection = (xMax - xMin) * (yMax - yMin);
  const area1 = box1.width * box1.height;
  const area2 = box2.width * box2.height;

  // Validate area calculations
  if (area1 <= 0 || area2 <= 0) {
    return 0.0;
  }

  switch (overlapType) {
    case 'intersection-over-union':
    case 'jaccard':
      const union = area1 + area2 - intersection;
      if (union <= 0) {
        return 0.0;
      }
      const iou = intersection / union;
      
      // Clamp IoU to valid range [0, 1]
      return Math.max(0, Math.min(1, iou));
    default:
      return 0.0;
  }
}

/**
 * FIXED: Helper function to validate bounding box coordinates
 */
function isValidBoundingBox(box: any): boolean {
  return box &&
    typeof box.xMin === 'number' && !isNaN(box.xMin) &&
    typeof box.yMin === 'number' && !isNaN(box.yMin) &&
    typeof box.xMax === 'number' && !isNaN(box.xMax) &&
    typeof box.yMax === 'number' && !isNaN(box.yMax) &&
    typeof box.width === 'number' && !isNaN(box.width) &&
    typeof box.height === 'number' && !isNaN(box.height) &&
    box.width > 0 && box.height > 0 &&
    box.xMin < box.xMax && box.yMin < box.yMax;
}