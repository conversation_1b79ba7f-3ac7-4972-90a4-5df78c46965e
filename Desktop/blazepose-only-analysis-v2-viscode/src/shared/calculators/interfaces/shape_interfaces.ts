
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * Shape and geometric interfaces for BlazePose calculations.
 */

import * as tf from '@tensorflow/tfjs-core';

export interface Rect {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface NormalizedRect {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Padding {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface AnchorTensor {
  x: tf.Tensor1D;
  y: tf.Tensor1D;
  w: tf.Tensor1D;
  h: tf.Tensor1D;
}

export interface Anchor {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
}

export interface Detection {
  boundingBox: {
    xMin: number;
    yMin: number;
    xMax: number;
    yMax: number;
    width: number;
    height: number;
  };
  keypoints?: Array<{x: number; y: number; score?: number}>;
  score: number;
  locationData?: LocationData;
}

export interface BoundingBox {
  xMin: number;
  yMin: number;
  xMax: number;
  yMax: number;
  width: number;
  height: number;
}

export interface LocationData {
  format: string;
  boundingBox?: BoundingBox;
  relativeBoundingBox?: BoundingBox;
  relativeKeypoints?: Array<{x: number; y: number}>;
}
