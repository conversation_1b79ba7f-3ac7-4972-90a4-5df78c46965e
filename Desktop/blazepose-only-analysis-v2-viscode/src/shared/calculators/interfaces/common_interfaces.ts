
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * Common interfaces for pose detection
 */

import * as tf from '@tensorflow/tfjs-core';

export interface Keypoint {
  x: number;
  y: number;
  z?: number;
  score?: number;
  name?: string;
}

export interface Pose {
  keypoints: Keypoint[];
  keypoints3D?: Keypoint[];
  score?: number;
  id?: number;
  box?: BoundingBox;
}

export interface ImageSize {
  width: number;
  height: number;
}

export interface BoundingBox {
  xMin: number;
  yMin: number;
  xMax: number;
  yMax: number;
  width: number;
  height: number;
}

export interface Detection {
  boundingBox: BoundingBox;
  relativeBoundingBox?: Rect; // Required by BlazePose pipeline
  keypoints?: Keypoint[];
  score: number;
  locationData?: LocationData;
}

export interface LocationData {
  format: string;
  boundingBox?: BoundingBox;
  relativeBoundingBox?: BoundingBox;
  relativeKeypoints?: Array<{x: number; y: number}>;
}

export interface Rect {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
  rotation?: number;
}

/**
 * BlazePose specific interfaces
 */
export interface BlazePoseLandmark {
  x: number;
  y: number;
  z: number;
  visibility?: number;
  presence?: number;
}

export interface BlazePoseWorldLandmark {
  x: number;
  y: number;
  z: number;
  visibility?: number;
}

/**
 * Tensor processing interfaces
 */
export interface TensorProcessingResult {
  landmarks: Keypoint[];
  worldLandmarks?: Keypoint[];
  segmentationMask?: number[][];
}

/**
 * Additional interfaces for pose detection
 */
export interface Mask {
  toCanvasImageSource(): Promise<CanvasImageSource>;
  toImageData(): Promise<ImageData>;
  toTensor(): Promise<any>;
  getUnderlyingType(): string;
}

export interface Padding {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface WindowElement {
  distance: number;
  duration: number;
}

export interface KeypointsFilter {
  apply(keypoints: Keypoint[], microSeconds: number, objectScale: number): Keypoint[];
  reset(): void;
}

export interface PixelInput {
  width: number;
  height: number;
}

export interface Color {
  r: number;
  g: number;
  b: number;
  a?: number;
}

export interface Segmentation {
  maskValueToLabel: Map<number, string>;
  mask: ImageData | tf.Tensor2D;
}

export type ImageType = HTMLImageElement | HTMLVideoElement | HTMLCanvasElement | ImageData;

export interface InputResolution {
  width: number;
  height: number;
}
