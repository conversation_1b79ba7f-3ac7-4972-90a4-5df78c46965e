/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * Tensor conversion and processing interfaces
 */

import * as tf from '@tensorflow/tfjs-core';
import { ImageSize } from './common_interfaces';

export interface ImageToTensorConfig {
  outputTensorSize: { width: number; height: number };
  keepAspectRatio: boolean;
  outputTensorFloatRange: number[];
  borderMode: string;
}

export interface ImageToTensorResult {
  imageTensor: tf.Tensor3D;
  padding: { top: number; bottom: number; left: number; right: number };
  transformationMatrix?: number[];
}

export interface TensorsToLandmarksConfig {
  numLandmarks: number;
  inputImageWidth: number;
  inputImageHeight: number;
  normalizeCoordinates: boolean;
  flipHorizontally?: boolean;
  flipVertically?: boolean;
}

export interface TensorsToSegmentationConfig {
  activation: 'sigmoid' | 'softmax' | 'none';
}

export interface SegmentationSmoothingConfig {
  alpha: number;
}

export interface Matrix4x4Row extends Array<number> {
  0: number;
  1: number;
  2: number;
  3: number;
  length: 4;
}

export type Matrix4x4 = [Matrix4x4Row, Matrix4x4Row, Matrix4x4Row, Matrix4x4Row];