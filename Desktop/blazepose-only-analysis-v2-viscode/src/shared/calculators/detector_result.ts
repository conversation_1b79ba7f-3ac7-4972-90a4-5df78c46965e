
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';

/**
 * PHASE 1: Detector result processing for BlazePose detection phase.
 */

export interface DetectorResult {
  boxes: tf.Tensor2D;
  logits: tf.Tensor2D;
}

/**
 * Extracts detector results from raw model output tensors.
 */
export function detectorResult(
    detectionTensors: tf.Tensor[]): DetectorResult {
  
  // TASK 5: Comprehensive logging starts with performance timing
  const startTime = performance.now();
  // Processing detector output tensors (logging disabled for performance)
  const ENABLE_DETAILED_LOGGING = false; // Set to true only for debugging

  // Enhanced validation: Check for empty input
  if (detectionTensors.length < 1) {
    console.error('🔧 DETECTOR RESULT: No tensors provided - returning zero tensors');
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }

  try {
    const mainTensor = detectionTensors[0];
    // Main tensor processing

    // Enhanced validation: Validate tensor format for BlazePose
    if (mainTensor.shape.length !== 3) {
      console.error('🔧 DETECTOR RESULT: Expected 3D tensor, got shape:', mainTensor.shape);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    const [batchSize, numAnchors, numValues] = mainTensor.shape;
    // Tensor dimensions processed

    // TASK 1: BlazePose tensor format validation (logging disabled)
    
    // Validate batch dimension
    if (batchSize !== 1) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires batch size 1, got:', batchSize);
      console.error('🔧 DETECTOR RESULT: BlazePose detection phase expects single-batch processing');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // Validate anchor count (should be reasonable for BlazePose)
    if (numAnchors <= 0) {
      console.error('🔧 DETECTOR RESULT: Invalid anchor count:', numAnchors);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // TASK 1: Strict BlazePose format validation for 13-value structure
    if (numValues !== 13) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires exactly 13 values per detection, got:', numValues);
      console.error('🔧 DETECTOR RESULT: Expected format: [yCenter, xCenter, height, width, score, ...8 additional values]');
      console.error('🔧 DETECTOR RESULT: This tensor does not match BlazePose detection output specification');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    if (ENABLE_DETAILED_LOGGING) {
      console.log('✅ DETECTOR RESULT: BlazePose tensor format validation passed');
      console.log('🔧 DETECTOR RESULT: Confirmed format: [batch=1, anchors=' + numAnchors + ', values=13]');
      console.log('🔧 DETECTOR RESULT: Value structure: [4 box coords] + [1 score] + [8 additional values]');
    }

    // Handle BlazePose single tensor format: [batch, anchors, 13]
    // Where 13 = [4 box coords, 1 score, 8 additional values]
    if (mainTensor.shape.length === 3 && mainTensor.shape[2] >= 5) {
      if (ENABLE_DETAILED_LOGGING) {
        console.log('🔧 DETECTOR RESULT: Processing BlazePose single tensor format');
        console.log('🔧 DETECTOR RESULT: Starting tensor slicing verification');
        console.log('🔧 DETECTOR RESULT: BlazePose coordinate order: [yCenter, xCenter, height, width] at indices [0,1,2,3]');
        console.log('🔧 DETECTOR RESULT: BlazePose score at index [4]');
      }
      
      // TASK 2: Validate slicing parameters before execution
      const sliceStart = [0, 0, 0];
      const sliceSize = [-1, -1, 4]; // -1 means "all elements in that dimension"
      const scoreStart = [0, 0, 4];
      const scoreSize = [-1, -1, 1];
      
      // Slice parameters configured
      
      // Validate slice bounds
      if (mainTensor.shape[2] < 5) {
        console.error('🔧 DETECTOR RESULT: Tensor too small for slicing - need at least 5 values, got:', mainTensor.shape[2]);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      // Executing tensor slicing and validation (logging disabled for performance)
      try {
        // Test tensor metadata accessibility
        const tensorMetadata = {
          dtype: mainTensor.dtype,
          size: mainTensor.size,
          rank: mainTensor.rank,
          memory: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`
        };
        // Tensor metadata validated
        
        // Test tensor bounds before slicing
        const [batch, anchors, values] = mainTensor.shape;
        const totalElements = batch * anchors * values;
        
        if (totalElements !== mainTensor.size) {
          console.error('🔧 DETECTOR RESULT: Tensor size mismatch - shape implies', totalElements, 'but size is', mainTensor.size);
          return {
            boxes: tf.zeros([1, 4]) as tf.Tensor2D,
            logits: tf.zeros([1, 1]) as tf.Tensor2D
          };
        }
        
        // Tensor integrity tests passed
        
      } catch (metadataError) {
        console.error('🔧 DETECTOR RESULT: Tensor metadata access failed:', metadataError);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }

      // TASK 2: Execute slicing with explicit BlazePose coordinate interpretation
      // Slice boxes: Extract first 4 values [yCenter, xCenter, height, width]
      const boxes = tf.slice(mainTensor, sliceStart, sliceSize) as tf.Tensor3D;
      // Slice scores: Extract 5th value [score]
      const scores = tf.slice(mainTensor, scoreStart, scoreSize) as tf.Tensor3D;
      
      // TASK 4: Post-slicing indexing validation
      console.log('🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...');
      
      // Test tensor element accessibility
      try {
        const boxesShape = boxes.shape;
        const scoresShape = scores.shape;
        
        // Validate indexing bounds
        const expectedBoxElements = boxesShape[0] * boxesShape[1] * boxesShape[2];
        const expectedScoreElements = scoresShape[0] * scoresShape[1] * scoresShape[2];
        
        if (boxes.size !== expectedBoxElements) {
          console.error('🔧 DETECTOR RESULT: Boxes tensor indexing mismatch - expected', expectedBoxElements, 'got', boxes.size);
        }
        
        if (scores.size !== expectedScoreElements) {
          console.error('🔧 DETECTOR RESULT: Scores tensor indexing mismatch - expected', expectedScoreElements, 'got', scores.size);
        }
        
        // Test small sample data extraction to verify indexing
        if (boxesShape[1] > 0) {
          const testSlice = tf.slice(boxes, [0, 0, 0], [1, 1, 4]);
          const testData = testSlice.dataSync();
          testSlice.dispose();
          
          console.log('🔧 DETECTOR RESULT: Index validation - first detection coordinates:', 
            Array.from(testData).map(v => v.toFixed(4)));
        }
        
        console.log('✅ DETECTOR RESULT: Tensor indexing validation passed');
        
      } catch (indexingError) {
        console.error('🔧 DETECTOR RESULT: Tensor indexing validation failed:', indexingError);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      // TASK 2: Validate slicing results
      console.log('🔧 DETECTOR RESULT: Slicing completed successfully');
      console.log('🔧 DETECTOR RESULT: Boxes tensor shape (3D):', boxes.shape);
      console.log('🔧 DETECTOR RESULT: Scores tensor shape (3D):', scores.shape);
      
      // Verify shapes match expectations
      if (boxes.shape[2] !== 4) {
        console.error('🔧 DETECTOR RESULT: Box slice failed - expected 4 coordinates, got:', boxes.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      if (scores.shape[2] !== 1) {
        console.error('🔧 DETECTOR RESULT: Score slice failed - expected 1 score, got:', scores.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      console.log('✅ DETECTOR RESULT: Tensor slicing validation passed');
      
      // TASK 3: Validate anchor-based coordinate transformation readiness
      console.log('🔧 DETECTOR RESULT: Starting coordinate transformation validation');
      console.log('🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility');
      
      // Sample coordinate data for validation (first few detections)
      try {
        const sampleSize = Math.min(3, boxes.shape[1]); // Check first 3 detections or all if fewer
        const sampleSlice = tf.slice(boxes, [0, 0, 0], [1, sampleSize, 4]);
        const sampleData = sampleSlice.dataSync();
        sampleSlice.dispose();
        
        // Sample coordinate validation (logging disabled for performance)
        for (let i = 0; i < sampleSize; i++) {
          const baseIdx = i * 4;
          const coords = {
            yCenter: sampleData[baseIdx + 0],
            xCenter: sampleData[baseIdx + 1],
            height: sampleData[baseIdx + 2],
            width: sampleData[baseIdx + 3]
          };
          
          // TASK 3: Validate coordinate ranges for anchor transformation
          const isValidCoords = 
            isFinite(coords.yCenter) && isFinite(coords.xCenter) && 
            isFinite(coords.height) && isFinite(coords.width);
            
          if (!isValidCoords) {
            console.warn(`🔧 DETECTOR RESULT: Invalid coordinates at detection ${i} - contains non-finite values`);
          }
          
          // BlazePose typically outputs relative coordinates - check reasonable ranges
          const hasReasonableValues = 
            Math.abs(coords.yCenter) < 100 && Math.abs(coords.xCenter) < 100 &&
            coords.height > 0 && coords.width > 0 &&
            coords.height < 100 && coords.width < 100;
            
          if (!hasReasonableValues) {
            console.warn(`🔧 DETECTOR RESULT: Unusual coordinate values at detection ${i} - may affect anchor transformation`);
          }
        }
        
        // Coordinate format verification completed
        
      } catch (validationError) {
        console.warn('🔧 DETECTOR RESULT: Could not validate coordinate data:', validationError);
      }
      
      // Coordinate transformation validation completed
      
      // Reshape to 2D for consistency with downstream processing
      const boxes2D = tf.reshape(boxes, [boxes.shape[1], 4]) as tf.Tensor2D;
      const logits2D = tf.reshape(scores, [scores.shape[1], 1]) as tf.Tensor2D;
      
      // Final tensor validation and reshaping completed (logging disabled for performance)
      
      // Clean up intermediate tensors
      boxes.dispose();
      scores.dispose();
      
      // TASK 5: Comprehensive logging - Performance and statistical analysis
      const processingTime = performance.now() - startTime;
      console.log('🔧 DETECTOR RESULT: Processing completed successfully');
      console.log('🔧 DETECTOR RESULT: Performance metrics:', {
        totalTime: `${processingTime.toFixed(2)}ms`,
        tensorsProcessed: 1,
        detectionsExtracted: boxes2D.shape[0],
        avgTimePerDetection: `${(processingTime / boxes2D.shape[0]).toFixed(3)}ms`
      });
      
      // TASK 5: Statistical analysis of extracted data
      try {
        const boxSample = tf.slice(boxes2D, [0, 0], [Math.min(5, boxes2D.shape[0]), 4]);
        const scoreSample = tf.slice(logits2D, [0, 0], [Math.min(5, logits2D.shape[0]), 1]);
        
        const boxData = boxSample.dataSync();
        const scoreData = scoreSample.dataSync();
        
        boxSample.dispose();
        scoreSample.dispose();
        
        const boxStats = {
          min: Math.min(...boxData),
          max: Math.max(...boxData),
          mean: Array.from(boxData).reduce((sum, val) => sum + val, 0) / boxData.length,
          validCount: Array.from(boxData).filter(v => isFinite(v)).length
        };
        
        const scoreStats = {
          min: Math.min(...scoreData),
          max: Math.max(...scoreData),
          mean: Array.from(scoreData).reduce((sum, val) => sum + val, 0) / scoreData.length,
          validCount: Array.from(scoreData).filter(v => isFinite(v)).length
        };
        
        // Data quality analysis completed (logging disabled for performance)
        
      } catch (statsError) {
        console.warn('🔧 DETECTOR RESULT: Could not analyze extracted data:', statsError);
      }
      
      // TASK 5: Memory usage summary
      const memoryUsage = {
        inputTensor: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`,
        outputBoxes: `${(boxes2D.size * 4 / 1024).toFixed(2)} KB`,
        outputLogits: `${(logits2D.size * 4 / 1024).toFixed(2)} KB`,
        totalOutput: `${((boxes2D.size + logits2D.size) * 4 / 1024).toFixed(2)} KB`
      };
      
      // Processing completed successfully (logging disabled for performance)
      
      return {
        boxes: boxes2D,
        logits: logits2D
      };
    }
    
    // Handle multi-tensor format (fallback)
    else if (detectionTensors.length >= 2) {
      // Processing multi-tensor format
      const boxes = detectionTensors[0] as tf.Tensor2D;
      const logits = detectionTensors[1] as tf.Tensor2D;

      return {
        boxes,
        logits
      };
    }
    
    // Unsupported format
    else {
      console.warn('🔧 DETECTOR RESULT: Unsupported tensor format');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Error processing detector result:', error);
    console.error('🔧 DETECTOR RESULT: Input tensor count:', detectionTensors.length);
    if (detectionTensors.length > 0) {
      console.error('🔧 DETECTOR RESULT: Main tensor shape:', detectionTensors[0].shape);
    }
    console.error('🔧 DETECTOR RESULT: Returning zero tensors due to processing error');
    
    // Return safe fallback tensors
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }
}

/**
 * Validates detector result tensors.
 */
export function validateDetectorResult(result: DetectorResult): boolean {
  try {
    if (!result.boxes || !result.logits) {
      console.warn('🔧 DETECTOR RESULT: Missing boxes or logits tensor');
      return false;
    }

    if (result.boxes.shape.length !== 2 || result.logits.shape.length !== 2) {
      console.warn('🔧 DETECTOR RESULT: Invalid tensor dimensions');
      return false;
    }

    // Validation passed
    return true;

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Validation error:', error);
    return false;
  }
}
