
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';

/**
 * PHASE 1: Detector result processing for BlazePose detection phase.
 */

export interface DetectorResult {
  boxes: tf.Tensor2D;
  logits: tf.Tensor2D;
}

/**
 * Extracts detector results from raw model output tensors.
 */
export function detectorResult(
    detectionTensors: tf.Tensor[]): DetectorResult {
  
  // TASK 5: Comprehensive logging starts with performance timing
  const startTime = performance.now();
  console.log('🔧 DETECTOR RESULT: Processing detector output tensors');
  console.log('🔧 DETECTOR RESULT: Input tensors count:', detectionTensors.length);
  console.log('🔧 DETECTOR RESULT: Processing started at:', new Date().toISOString());

  // Enhanced validation: Check for empty input
  if (detectionTensors.length < 1) {
    console.error('🔧 DETECTOR RESULT: No tensors provided - returning zero tensors');
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }

  try {
    const mainTensor = detectionTensors[0];
    console.log('🔧 DETECTOR RESULT: Main tensor shape:', mainTensor.shape);

    // Enhanced validation: Validate tensor format for BlazePose
    if (mainTensor.shape.length !== 3) {
      console.error('🔧 DETECTOR RESULT: Expected 3D tensor, got shape:', mainTensor.shape);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    const [batchSize, numAnchors, numValues] = mainTensor.shape;
    console.log('🔧 DETECTOR RESULT: Tensor dimensions - batch:', batchSize, 'anchors:', numAnchors, 'values:', numValues);

    // TASK 1: Comprehensive BlazePose tensor format validation
    console.log('🔧 DETECTOR RESULT: Starting BlazePose tensor format verification');
    
    // Validate batch dimension
    if (batchSize !== 1) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires batch size 1, got:', batchSize);
      console.error('🔧 DETECTOR RESULT: BlazePose detection phase expects single-batch processing');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // Validate anchor count (should be reasonable for BlazePose)
    if (numAnchors <= 0) {
      console.error('🔧 DETECTOR RESULT: Invalid anchor count:', numAnchors);
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    // TASK 1: Strict BlazePose format validation for 13-value structure
    if (numValues !== 13) {
      console.error('🔧 DETECTOR RESULT: BlazePose requires exactly 13 values per detection, got:', numValues);
      console.error('🔧 DETECTOR RESULT: Expected format: [yCenter, xCenter, height, width, score, ...8 additional values]');
      console.error('🔧 DETECTOR RESULT: This tensor does not match BlazePose detection output specification');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

    console.log('✅ DETECTOR RESULT: BlazePose tensor format validation passed');
    console.log('🔧 DETECTOR RESULT: Confirmed format: [batch=1, anchors=' + numAnchors + ', values=13]');
    console.log('🔧 DETECTOR RESULT: Value structure: [4 box coords] + [1 score] + [8 additional values]');

    // Handle BlazePose single tensor format: [batch, anchors, 13]
    // Where 13 = [4 box coords, 1 score, 8 additional values]
    if (mainTensor.shape.length === 3 && mainTensor.shape[2] >= 5) {
      console.log('🔧 DETECTOR RESULT: Processing BlazePose single tensor format');
      
      // TASK 2: Verify correct slicing for boxes (indices 0-3) and scores (index 4)
      console.log('🔧 DETECTOR RESULT: Starting tensor slicing verification');
      console.log('🔧 DETECTOR RESULT: BlazePose coordinate order: [yCenter, xCenter, height, width] at indices [0,1,2,3]');
      console.log('🔧 DETECTOR RESULT: BlazePose score at index [4]');
      
      // TASK 2: Validate slicing parameters before execution
      const sliceStart = [0, 0, 0];
      const sliceSize = [-1, -1, 4]; // -1 means "all elements in that dimension"
      const scoreStart = [0, 0, 4];
      const scoreSize = [-1, -1, 1];
      
      console.log('🔧 DETECTOR RESULT: Box slice parameters:', { start: sliceStart, size: sliceSize });
      console.log('🔧 DETECTOR RESULT: Score slice parameters:', { start: scoreStart, size: scoreSize });
      
      // Validate slice bounds
      if (mainTensor.shape[2] < 5) {
        console.error('🔧 DETECTOR RESULT: Tensor too small for slicing - need at least 5 values, got:', mainTensor.shape[2]);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      console.log('🔧 DETECTOR RESULT: Executing tensor slicing...');
      
      // TASK 4: Comprehensive tensor data extraction and indexing tests
      console.log('🔧 DETECTOR RESULT: Starting comprehensive tensor extraction testing');
      
      // TASK 4: Pre-slicing validation - verify tensor integrity
      console.log('🔧 DETECTOR RESULT: Testing tensor accessibility...');
      try {
        // Test tensor metadata accessibility
        const tensorMetadata = {
          dtype: mainTensor.dtype,
          size: mainTensor.size,
          rank: mainTensor.rank,
          memory: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`
        };
        console.log('🔧 DETECTOR RESULT: Tensor metadata validated:', tensorMetadata);
        
        // Test tensor bounds before slicing
        const [batch, anchors, values] = mainTensor.shape;
        const totalElements = batch * anchors * values;
        
        if (totalElements !== mainTensor.size) {
          console.error('🔧 DETECTOR RESULT: Tensor size mismatch - shape implies', totalElements, 'but size is', mainTensor.size);
          return {
            boxes: tf.zeros([1, 4]) as tf.Tensor2D,
            logits: tf.zeros([1, 1]) as tf.Tensor2D
          };
        }
        
        console.log('✅ DETECTOR RESULT: Tensor integrity tests passed');
        
      } catch (metadataError) {
        console.error('🔧 DETECTOR RESULT: Tensor metadata access failed:', metadataError);
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }

      // TASK 2: Execute slicing with explicit BlazePose coordinate interpretation
      // Slice boxes: Extract first 4 values [yCenter, xCenter, height, width]
      const boxes = tf.slice(mainTensor, sliceStart, sliceSize) as tf.Tensor3D;
      // Slice scores: Extract 5th value [score]
      const scores = tf.slice(mainTensor, scoreStart, scoreSize) as tf.Tensor3D;
      
      // TASK 4: Post-slicing indexing validation
      console.log('🔧 DETECTOR RESULT: Testing post-slicing tensor indexing...');
      
      // Test tensor element accessibility
      try {
        const boxesShape = boxes.shape;
        const scoresShape = scores.shape;
        
        // Validate indexing bounds
        const expectedBoxElements = boxesShape[0] * boxesShape[1] * boxesShape[2];
        const expectedScoreElements = scoresShape[0] * scoresShape[1] * scoresShape[2];
        
        if (boxes.size !== expectedBoxElements) {
          console.error('🔧 DETECTOR RESULT: Boxes tensor indexing mismatch - expected', expectedBoxElements, 'got', boxes.size);
        }
        
        if (scores.size !== expectedScoreElements) {
          console.error('🔧 DETECTOR RESULT: Scores tensor indexing mismatch - expected', expectedScoreElements, 'got', scores.size);
        }
        
        // Test small sample data extraction to verify indexing
        if (boxesShape[1] > 0) {
          const testSlice = tf.slice(boxes, [0, 0, 0], [1, 1, 4]);
          const testData = testSlice.dataSync();
          testSlice.dispose();
          
          console.log('🔧 DETECTOR RESULT: Index validation - first detection coordinates:', 
            Array.from(testData).map(v => v.toFixed(4)));
        }
        
        console.log('✅ DETECTOR RESULT: Tensor indexing validation passed');
        
      } catch (indexingError) {
        console.error('🔧 DETECTOR RESULT: Tensor indexing validation failed:', indexingError);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      // TASK 2: Validate slicing results
      console.log('🔧 DETECTOR RESULT: Slicing completed successfully');
      console.log('🔧 DETECTOR RESULT: Boxes tensor shape (3D):', boxes.shape);
      console.log('🔧 DETECTOR RESULT: Scores tensor shape (3D):', scores.shape);
      
      // Verify shapes match expectations
      if (boxes.shape[2] !== 4) {
        console.error('🔧 DETECTOR RESULT: Box slice failed - expected 4 coordinates, got:', boxes.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      if (scores.shape[2] !== 1) {
        console.error('🔧 DETECTOR RESULT: Score slice failed - expected 1 score, got:', scores.shape[2]);
        boxes.dispose();
        scores.dispose();
        return {
          boxes: tf.zeros([1, 4]) as tf.Tensor2D,
          logits: tf.zeros([1, 1]) as tf.Tensor2D
        };
      }
      
      console.log('✅ DETECTOR RESULT: Tensor slicing validation passed');
      
      // TASK 3: Validate anchor-based coordinate transformation readiness
      console.log('🔧 DETECTOR RESULT: Starting coordinate transformation validation');
      console.log('🔧 DETECTOR RESULT: Validating extracted coordinates for anchor transformation compatibility');
      
      // Sample coordinate data for validation (first few detections)
      try {
        const sampleSize = Math.min(3, boxes.shape[1]); // Check first 3 detections or all if fewer
        const sampleSlice = tf.slice(boxes, [0, 0, 0], [1, sampleSize, 4]);
        const sampleData = sampleSlice.dataSync();
        sampleSlice.dispose();
        
        console.log('🔧 DETECTOR RESULT: Sample coordinate data for validation:');
        for (let i = 0; i < sampleSize; i++) {
          const baseIdx = i * 4;
          const coords = {
            yCenter: sampleData[baseIdx + 0],
            xCenter: sampleData[baseIdx + 1], 
            height: sampleData[baseIdx + 2],
            width: sampleData[baseIdx + 3]
          };
          
          console.log(`🔧 DETECTOR RESULT: Detection ${i} coords:`, {
            yCenter: coords.yCenter.toFixed(4),
            xCenter: coords.xCenter.toFixed(4),
            height: coords.height.toFixed(4),
            width: coords.width.toFixed(4)
          });
          
          // TASK 3: Validate coordinate ranges for anchor transformation
          const isValidCoords = 
            isFinite(coords.yCenter) && isFinite(coords.xCenter) && 
            isFinite(coords.height) && isFinite(coords.width);
            
          if (!isValidCoords) {
            console.warn(`🔧 DETECTOR RESULT: Invalid coordinates at detection ${i} - contains non-finite values`);
          }
          
          // BlazePose typically outputs relative coordinates - check reasonable ranges
          const hasReasonableValues = 
            Math.abs(coords.yCenter) < 100 && Math.abs(coords.xCenter) < 100 &&
            coords.height > 0 && coords.width > 0 &&
            coords.height < 100 && coords.width < 100;
            
          if (!hasReasonableValues) {
            console.warn(`🔧 DETECTOR RESULT: Unusual coordinate values at detection ${i} - may affect anchor transformation`);
          }
        }
        
        console.log('🔧 DETECTOR RESULT: Coordinate format verification:');
        console.log('🔧 DETECTOR RESULT: ✓ yCenter at index [0] - relative Y coordinate for anchor transformation');
        console.log('🔧 DETECTOR RESULT: ✓ xCenter at index [1] - relative X coordinate for anchor transformation'); 
        console.log('🔧 DETECTOR RESULT: ✓ height at index [2] - relative height for anchor scaling');
        console.log('🔧 DETECTOR RESULT: ✓ width at index [3] - relative width for anchor scaling');
        console.log('🔧 DETECTOR RESULT: ✓ Coordinate order matches BlazePose anchor transformation expectations');
        
      } catch (validationError) {
        console.warn('🔧 DETECTOR RESULT: Could not validate coordinate data:', validationError);
      }
      
      console.log('✅ DETECTOR RESULT: Coordinate transformation validation completed');
      
      // Reshape to 2D for consistency with downstream processing
      const boxes2D = tf.reshape(boxes, [boxes.shape[1], 4]) as tf.Tensor2D;
      const logits2D = tf.reshape(scores, [scores.shape[1], 1]) as tf.Tensor2D;
      
      // TASK 3: Final validation of output tensors for anchor pipeline integration
      console.log('🔧 DETECTOR RESULT: Final tensor validation for anchor transformation:');
      console.log('🔧 DETECTOR RESULT: Boxes tensor format: [numDetections=' + boxes2D.shape[0] + ', coordinates=4]');
      console.log('🔧 DETECTOR RESULT: Logits tensor format: [numDetections=' + logits2D.shape[0] + ', scores=1]');
      console.log('🔧 DETECTOR RESULT: ✓ Tensor shapes compatible with anchor-based coordinate transformation');
      console.log('🔧 DETECTOR RESULT: ✓ Ready for downstream tensors_to_detections processing');
      
      console.log('🔧 DETECTOR RESULT: Reshaped boxes to 2D:', boxes2D.shape);
      console.log('🔧 DETECTOR RESULT: Reshaped logits to 2D:', logits2D.shape);
      console.log('🔧 DETECTOR RESULT: Coordinate interpretation: boxes2D[:, 0]=yCenter, [:, 1]=xCenter, [:, 2]=height, [:, 3]=width');
      console.log('🔧 DETECTOR RESULT: Score interpretation: logits2D[:, 0]=confidence_score');
      console.log('🔧 DETECTOR RESULT: Pipeline integration - tensors ready for downstream processing');
      
      // Clean up intermediate tensors
      boxes.dispose();
      scores.dispose();
      
      // TASK 5: Comprehensive logging - Performance and statistical analysis
      const processingTime = performance.now() - startTime;
      console.log('🔧 DETECTOR RESULT: Processing completed successfully');
      console.log('🔧 DETECTOR RESULT: Performance metrics:', {
        totalTime: `${processingTime.toFixed(2)}ms`,
        tensorsProcessed: 1,
        detectionsExtracted: boxes2D.shape[0],
        avgTimePerDetection: `${(processingTime / boxes2D.shape[0]).toFixed(3)}ms`
      });
      
      // TASK 5: Statistical analysis of extracted data
      try {
        const boxSample = tf.slice(boxes2D, [0, 0], [Math.min(5, boxes2D.shape[0]), 4]);
        const scoreSample = tf.slice(logits2D, [0, 0], [Math.min(5, logits2D.shape[0]), 1]);
        
        const boxData = boxSample.dataSync();
        const scoreData = scoreSample.dataSync();
        
        boxSample.dispose();
        scoreSample.dispose();
        
        const boxStats = {
          min: Math.min(...boxData),
          max: Math.max(...boxData),
          mean: Array.from(boxData).reduce((sum, val) => sum + val, 0) / boxData.length,
          validCount: Array.from(boxData).filter(v => isFinite(v)).length
        };
        
        const scoreStats = {
          min: Math.min(...scoreData),
          max: Math.max(...scoreData),
          mean: Array.from(scoreData).reduce((sum, val) => sum + val, 0) / scoreData.length,
          validCount: Array.from(scoreData).filter(v => isFinite(v)).length
        };
        
        console.log('🔧 DETECTOR RESULT: Data quality analysis:', {
          boxStats: {
            ...boxStats,
            mean: boxStats.mean.toFixed(4),
            validRatio: `${(boxStats.validCount / boxData.length * 100).toFixed(1)}%`
          },
          scoreStats: {
            ...scoreStats,
            mean: scoreStats.mean.toFixed(4),
            validRatio: `${(scoreStats.validCount / scoreData.length * 100).toFixed(1)}%`
          }
        });
        
      } catch (statsError) {
        console.warn('🔧 DETECTOR RESULT: Could not analyze extracted data:', statsError);
      }
      
      // TASK 5: Memory usage summary
      const memoryUsage = {
        inputTensor: `${(mainTensor.size * 4 / 1024).toFixed(2)} KB`,
        outputBoxes: `${(boxes2D.size * 4 / 1024).toFixed(2)} KB`,
        outputLogits: `${(logits2D.size * 4 / 1024).toFixed(2)} KB`,
        totalOutput: `${((boxes2D.size + logits2D.size) * 4 / 1024).toFixed(2)} KB`
      };
      
      console.log('🔧 DETECTOR RESULT: Memory usage summary:', memoryUsage);
      console.log('🔧 DETECTOR RESULT: Processing summary:', {
        status: 'SUCCESS',
        format: 'BlazePose single tensor',
        inputShape: mainTensor.shape,
        outputShapes: {
          boxes: boxes2D.shape,
          logits: logits2D.shape
        },
        processingTime: `${processingTime.toFixed(2)}ms`,
        timestamp: new Date().toISOString()
      });
      
      return {
        boxes: boxes2D,
        logits: logits2D
      };
    }
    
    // Handle multi-tensor format (fallback)
    else if (detectionTensors.length >= 2) {
      console.log('🔧 DETECTOR RESULT: Processing multi-tensor format');
      
      const boxes = detectionTensors[0] as tf.Tensor2D;
      const logits = detectionTensors[1] as tf.Tensor2D;

      console.log('🔧 DETECTOR RESULT: Boxes tensor shape:', boxes.shape);
      console.log('🔧 DETECTOR RESULT: Logits tensor shape:', logits.shape);

      return {
        boxes,
        logits
      };
    }
    
    // Unsupported format
    else {
      console.warn('🔧 DETECTOR RESULT: Unsupported tensor format');
      return {
        boxes: tf.zeros([1, 4]) as tf.Tensor2D,
        logits: tf.zeros([1, 1]) as tf.Tensor2D
      };
    }

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Error processing detector result:', error);
    console.error('🔧 DETECTOR RESULT: Input tensor count:', detectionTensors.length);
    if (detectionTensors.length > 0) {
      console.error('🔧 DETECTOR RESULT: Main tensor shape:', detectionTensors[0].shape);
    }
    console.error('🔧 DETECTOR RESULT: Returning zero tensors due to processing error');
    
    // Return safe fallback tensors
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }
}

/**
 * Validates detector result tensors.
 */
export function validateDetectorResult(result: DetectorResult): boolean {
  try {
    if (!result.boxes || !result.logits) {
      console.warn('🔧 DETECTOR RESULT: Missing boxes or logits tensor');
      return false;
    }

    if (result.boxes.shape.length !== 2 || result.logits.shape.length !== 2) {
      console.warn('🔧 DETECTOR RESULT: Invalid tensor dimensions');
      return false;
    }

    console.log('🔧 DETECTOR RESULT: Validation passed');
    return true;

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Validation error:', error);
    return false;
  }
}
