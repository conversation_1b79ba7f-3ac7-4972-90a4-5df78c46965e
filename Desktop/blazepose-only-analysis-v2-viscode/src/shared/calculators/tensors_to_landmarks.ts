
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';

/**
 * Configuration for tensors to landmarks conversion
 */
export interface TensorsToLandmarksConfig {
  numLandmarks: number;
  inputImageWidth: number;
  inputImageHeight: number;
  normalizeCoordinates: boolean;
  flipHorizontally?: boolean;
  flipVertically?: boolean;
}

/**
 * Converts pose landmark tensors to keypoints array.
 * This is the critical missing function from BlazePose pipeline.
 */
export async function tensorsToLandmarks(
    landmarkTensor: tf.Tensor,
    config: TensorsToLandmarksConfig): Promise<Keypoint[]> {
  
  console.log('🔧 TENSORS_TO_LANDMARKS: Converting tensor to landmarks');
  console.log('🔧 TENSORS_TO_LANDMARKS: Tensor shape:', landmarkTensor.shape);
  console.log('🔧 TENSORS_TO_LANDMARKS: Config:', config);

  const landmarkTensor2d = landmarkTensor.squeeze() as tf.Tensor2D;
  
  // Extract coordinates - BlazePose outputs [x, y, z, visibility, presence] per landmark
  const numLandmarks = config.numLandmarks;
  const landmarks: Keypoint[] = [];
  
  // Process tensor data
  const data: number[][] = await landmarkTensor2d.array();
  console.log('🔧 TENSORS_TO_LANDMARKS: Processing', data.length, 'landmarks');
  
  for (let i = 0; i < Math.min(numLandmarks, data.length); i++) {
    const landmarkData = data[i];
    
    if (!landmarkData || landmarkData.length < 3) {
      console.warn('🔧 TENSORS_TO_LANDMARKS: Invalid landmark data at index', i);
      continue;
    }
    
    let x = landmarkData[0];
    let y = landmarkData[1];
    const z = landmarkData[2] || 0;
    const visibility = landmarkData.length > 3 ? landmarkData[3] : 0.5;
    const presence = landmarkData.length > 4 ? landmarkData[4] : 0.5;
    
    // Normalize coordinates if needed
    if (config.normalizeCoordinates) {
      x = x / config.inputImageWidth;
      y = y / config.inputImageHeight;
    }
    
    // Apply flipping if configured
    if (config.flipHorizontally) {
      x = 1.0 - x;
    }
    if (config.flipVertically) {
      y = 1.0 - y;
    }
    
    // Validate coordinates
    if (isNaN(x) || isNaN(y)) {
      console.warn('🔧 TENSORS_TO_LANDMARKS: NaN coordinates at landmark', i);
      x = isNaN(x) ? 0.5 : x;
      y = isNaN(y) ? 0.5 : y;
    }
    
    landmarks.push({
      x: Math.max(0, Math.min(1, x)),
      y: Math.max(0, Math.min(1, y)),
      z: z,
      score: Math.max(visibility, presence),
      name: `landmark_${i}`
    });
  }
  
  console.log('✅ TENSORS_TO_LANDMARKS: Converted', landmarks.length, 'landmarks');
  return landmarks;
}

/**
 * Converts world landmark tensors to 3D keypoints.
 */
export async function tensorsToWorldLandmarks(
    worldLandmarkTensor: tf.Tensor,
    config: Omit<TensorsToLandmarksConfig, 'normalizeCoordinates'>): Promise<Keypoint[]> {
  
  console.log('🔧 TENSORS_TO_WORLD_LANDMARKS: Converting tensor to world landmarks');
  console.log('🔧 TENSORS_TO_WORLD_LANDMARKS: Tensor shape:', worldLandmarkTensor.shape);

  const worldTensor2d = worldLandmarkTensor.squeeze() as tf.Tensor2D;
  
  const data: number[][] = await worldTensor2d.array();
  const worldLandmarks: Keypoint[] = [];
  
  for (let i = 0; i < Math.min(config.numLandmarks, data.length); i++) {
    const landmarkData = data[i];
    
    if (!landmarkData || landmarkData.length < 3) {
      continue;
    }
    
    let x = landmarkData[0] || 0;
    let y = landmarkData[1] || 0;
    let z = landmarkData[2] || 0;
    const score = landmarkData.length > 3 ? landmarkData[3] : 0.5;
    
    // Validate world coordinates
    if (isNaN(x)) x = 0;
    if (isNaN(y)) y = 0;
    if (isNaN(z)) z = 0;
    
    worldLandmarks.push({
      x: x,
      y: y,
      z: z,
      score: isNaN(score) ? 0.5 : score,
      name: `world_landmark_${i}`
    });
  }
  
  console.log('✅ TENSORS_TO_WORLD_LANDMARKS: Converted', worldLandmarks.length, 'world landmarks');
  return worldLandmarks;
}
