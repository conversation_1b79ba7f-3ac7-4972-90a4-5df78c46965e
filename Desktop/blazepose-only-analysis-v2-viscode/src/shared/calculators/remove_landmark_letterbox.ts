/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, Padding, ImageSize } from './interfaces/common_interfaces';

/**
 * Removes letterbox padding from landmark coordinates.
 */
export function removeLandmarkLetterbox(
    landmarks: Keypoint[],
    padding: Padding,
    imageSize?: ImageSize): Keypoint[] {
  
  console.log('🔧 LANDMARK LETTERBOX REMOVAL: Removing letterbox padding from landmarks');
  console.log('🔧 LANDMARK LETTERBOX REMOVAL: Padding:', padding);
  
  if (!landmarks || landmarks.length === 0) {
    return [];
  }
  
  return landmarks.map(landmark => {
    // Adjust coordinates based on padding
    const adjustedX = landmark.x - (padding.left || 0);
    const adjustedY = landmark.y - (padding.top || 0);
    
    return {
      ...landmark,
      x: Math.max(0, Math.min(1, adjustedX)),
      y: Math.max(0, Math.min(1, adjustedY))
    };
  });
}