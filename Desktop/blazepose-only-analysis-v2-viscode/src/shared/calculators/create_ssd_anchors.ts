
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * PHASE 1: SSD anchor generation for BlazePose detection phase.
 */

export interface SsdAnchorConfig {
  inputSizeWidth: number;
  inputSizeHeight: number;
  minScale: number;
  maxScale: number;
  anchorOffsetX: number;
  anchorOffsetY: number;
  numLayers: number;
  featureMapWidth: number[];
  featureMapHeight: number[];
  strides: number[];
  aspectRatios: number[];
  reduceBoxesInLowestLayer: boolean;
  interpolatedScaleAspectRatio: number;
  fixedAnchorSize: boolean;
}

export interface Anchor {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
}

/**
 * Creates SSD anchors for pose detection.
 */
export function createSsdAnchors(config: SsdAnchorConfig): Anchor[] {
  console.log('🔧 SSD ANCHORS: Creating anchors with config:', config);
  console.log('🔧 SSD ANCHORS: Feature map dimensions:', {
    heights: config.featureMapHeight,
    widths: config.featureMapWidth,
    numLayers: config.numLayers,
    aspectRatios: config.aspectRatios
  });
  
  const anchors: Anchor[] = [];
  let layerIndex = 0;

  while (layerIndex < config.numLayers) {
    const featureMapHeight = config.featureMapHeight[layerIndex];
    const featureMapWidth = config.featureMapWidth[layerIndex];
    
    console.log(`🔧 SSD ANCHORS: Layer ${layerIndex}: ${featureMapWidth}x${featureMapHeight}`);
    
    for (let y = 0; y < featureMapHeight; y++) {
      for (let x = 0; x < featureMapWidth; x++) {
        for (let aspectRatioIndex = 0; aspectRatioIndex < config.aspectRatios.length; aspectRatioIndex++) {
          const aspectRatio = config.aspectRatios[aspectRatioIndex];
          
          // Calculate anchor dimensions
          const stride = config.strides[layerIndex];
          const xCenter = (x + config.anchorOffsetX) / featureMapWidth;
          const yCenter = (y + config.anchorOffsetY) / featureMapHeight;
          
          // Scale calculation
          const scale = config.minScale + (config.maxScale - config.minScale) * layerIndex / (config.numLayers - 1);
          
          const width = scale * Math.sqrt(aspectRatio);
          const height = scale / Math.sqrt(aspectRatio);
          
          anchors.push({
            xCenter,
            yCenter,
            width,
            height
          });
        }
      }
    }
    layerIndex++;
  }

  console.log(`🔧 SSD ANCHORS: Created ${anchors.length} anchors`);
  return anchors;
}
