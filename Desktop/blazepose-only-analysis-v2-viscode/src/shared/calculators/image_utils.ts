
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { ImageSize } from './interfaces/common_interfaces';

/**
 * Gets the size of an image or video element.
 */
export function getImageSize(image: any): ImageSize {
  if (image == null) {
    return { width: 0, height: 0 };
  }

  // Check if it's a tensor
  if (image instanceof tf.Tensor && image.shape.length >= 3) {
    const [height, width] = image.shape.slice(0, 2);
    return { width, height };
  }

  if (image instanceof HTMLVideoElement) {
    return { width: image.videoWidth, height: image.videoHeight };
  }

  if (image instanceof HTMLImageElement || image instanceof HTMLCanvasElement) {
    return { width: image.width, height: image.height };
  }

  if (image instanceof ImageData) {
    return { width: image.width, height: image.height };
  }

  // Check if it's a tensor (backup check)
  if (image instanceof tf.Tensor && image.shape.length === 3) {
    return { width: image.shape[1], height: image.shape[0] };
  }

  return { width: image.width || 0, height: image.height || 0 };
}

/**
 * Converts an image to a tensor.
 */
export function toImageTensor(image: any): tf.Tensor3D {
  if (image instanceof tf.Tensor && image.shape.length === 3) {
    return image as tf.Tensor3D;
  }

  return tf.browser.fromPixels(image);
}

/**
 * Gets a projective transformation matrix for image transformation.
 * Converts 4x4 transformation matrix to 8-element projective transform.
 */
export function getProjectiveTransformMatrix(
    matrix: number[],
    inputSize: ImageSize,
    outputSize: ImageSize): number[] {
  
  console.log('🔧 PROJECTIVE TRANSFORM: Creating transformation matrix');
  console.log('🔧 PROJECTIVE TRANSFORM: Input size:', inputSize);
  console.log('🔧 PROJECTIVE TRANSFORM: Output size:', outputSize);
  
  // Extract transformation components from 4x4 matrix
  // Matrix is in column-major order: [m00, m10, m20, m30, m01, m11, m21, m31, ...]
  const m00 = matrix[0] || 1;
  const m01 = matrix[4] || 0;
  const m02 = matrix[12] || 0;
  const m10 = matrix[1] || 0;
  const m11 = matrix[5] || 1;
  const m12 = matrix[13] || 0;
  const m20 = matrix[3] || 0;
  const m21 = matrix[7] || 0;
  
  // Apply scale factors for coordinate system transformation
  const scaleX = outputSize.width / inputSize.width;
  const scaleY = outputSize.height / inputSize.height;
  
  // Create 8-element projective transformation matrix
  // Format: [a, b, c, d, e, f, g, h] representing:
  // x' = (ax + by + c) / (gx + hy + 1)
  // y' = (dx + ey + f) / (gx + hy + 1)
  const projMatrix = [
    m00 * scaleX,  // a: x scaling
    m01 * scaleX,  // b: x shearing
    m02 * scaleX,  // c: x translation
    m10 * scaleY,  // d: y shearing  
    m11 * scaleY,  // e: y scaling
    m12 * scaleY,  // f: y translation
    m20,           // g: perspective x
    m21            // h: perspective y
  ];
  
  console.log('🔧 PROJECTIVE TRANSFORM: Generated matrix:', projMatrix);
  return projMatrix;
}

/**
 * Normalizes radians to be within [-PI, PI].
 */
export function normalizeRadians(radians: number): number {
  let normalizedRadians = radians % (2 * Math.PI);
  if (normalizedRadians > Math.PI) {
    normalizedRadians -= 2 * Math.PI;
  } else if (normalizedRadians < -Math.PI) {
    normalizedRadians += 2 * Math.PI;
  }
  return normalizedRadians;
}

/**
 * Transforms value range from one range to another.
 */
export function transformValueRange(
  fromMin: number, fromMax: number, 
  toMin: number, toMax: number
): { scale: number; offset: number } {
  const scale = (toMax - toMin) / (fromMax - fromMin);
  const offset = toMin - fromMin * scale;
  return { scale, offset };
}
