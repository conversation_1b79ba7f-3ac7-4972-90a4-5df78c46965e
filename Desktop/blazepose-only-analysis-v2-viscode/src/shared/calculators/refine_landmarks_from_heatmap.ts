
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';

export interface RefineLandmarksFromHeatmapConfig {
  kernelSize: number;
  minConfidenceToRefine: number;
}

/**
 * Refines landmark coordinates using heatmap data for sub-pixel accuracy.
 * This function improves landmark precision by analyzing the heatmap around each point.
 */
export async function refineLandmarksFromHeatmap(
    landmarks: Keypoint[],
    heatmapTensor: tf.Tensor4D,
    config: RefineLandmarksFromHeatmapConfig): Promise<Keypoint[]> {
  
  console.log('🔧 HEATMAP REFINING: Starting refinement with config:', config);
  console.log('🔧 HEATMAP REFINING: Heatmap tensor shape:', heatmapTensor.shape);
  console.log('🔧 HEATMAP REFINING: Input landmarks count:', landmarks.length);

  if (!landmarks || landmarks.length === 0) {
    console.warn('🔧 HEATMAP REFINING: No landmarks to refine');
    return landmarks;
  }

  const [batchSize, heatmapHeight, heatmapWidth, numKeypoints] = heatmapTensor.shape;
  
  if (numKeypoints !== landmarks.length) {
    console.warn(`🔧 HEATMAP REFINING: Keypoint count mismatch - heatmap: ${numKeypoints}, landmarks: ${landmarks.length}`);
  }

  const refinedLandmarks: Keypoint[] = [];
  const heatmapData = await heatmapTensor.data();
  
  for (let i = 0; i < landmarks.length && i < numKeypoints; i++) {
    const landmark = landmarks[i];
    
    // Only refine landmarks with sufficient confidence
    if (landmark.score && landmark.score < config.minConfidenceToRefine) {
      refinedLandmarks.push(landmark);
      continue;
    }

    // Convert normalized coordinates to heatmap coordinates
    const heatmapX = Math.round(landmark.x * (heatmapWidth - 1));
    const heatmapY = Math.round(landmark.y * (heatmapHeight - 1));
    
    // Ensure coordinates are within bounds
    const clampedX = Math.max(0, Math.min(heatmapWidth - 1, heatmapX));
    const clampedY = Math.max(0, Math.min(heatmapHeight - 1, heatmapY));
    
    // Extract local window around the landmark
    const halfKernel = Math.floor(config.kernelSize / 2);
    const startX = Math.max(0, clampedX - halfKernel);
    const endX = Math.min(heatmapWidth - 1, clampedX + halfKernel);
    const startY = Math.max(0, clampedY - halfKernel);
    const endY = Math.min(heatmapHeight - 1, clampedY + halfKernel);
    
    // Find peak in local window
    let maxValue = -Infinity;
    let peakX = clampedX;
    let peakY = clampedY;
    
    for (let y = startY; y <= endY; y++) {
      for (let x = startX; x <= endX; x++) {
        const heatmapIndex = (batchSize * heatmapHeight * heatmapWidth * i) + 
                            (y * heatmapWidth) + x;
        const value = heatmapData[heatmapIndex];
        
        if (value > maxValue) {
          maxValue = value;
          peakX = x;
          peakY = y;
        }
      }
    }
    
    // Sub-pixel refinement using gradient
    let refinedX = peakX;
    let refinedY = peakY;
    
    // Calculate gradients for sub-pixel accuracy
    if (peakX > 0 && peakX < heatmapWidth - 1 && 
        peakY > 0 && peakY < heatmapHeight - 1) {
      
      const getHeatmapValue = (x: number, y: number): number => {
        const index = (batchSize * heatmapHeight * heatmapWidth * i) + 
                     (y * heatmapWidth) + x;
        return heatmapData[index];
      };
      
      // Calculate gradients
      const gradX = (getHeatmapValue(peakX + 1, peakY) - getHeatmapValue(peakX - 1, peakY)) / 2;
      const gradY = (getHeatmapValue(peakX, peakY + 1) - getHeatmapValue(peakX, peakY - 1)) / 2;
      
      // Calculate second derivatives for Hessian
      const hessXX = getHeatmapValue(peakX + 1, peakY) - 2 * maxValue + getHeatmapValue(peakX - 1, peakY);
      const hessYY = getHeatmapValue(peakX, peakY + 1) - 2 * maxValue + getHeatmapValue(peakX, peakY - 1);
      const hessXY = (getHeatmapValue(peakX + 1, peakY + 1) - getHeatmapValue(peakX + 1, peakY - 1) -
                     getHeatmapValue(peakX - 1, peakY + 1) + getHeatmapValue(peakX - 1, peakY - 1)) / 4;
      
      // Calculate determinant to avoid division by zero
      const det = hessXX * hessYY - hessXY * hessXY;
      
      if (Math.abs(det) > 1e-6) {
        // Sub-pixel correction using inverse Hessian
        const deltaX = -(hessYY * gradX - hessXY * gradY) / det;
        const deltaY = -(hessXX * gradY - hessXY * gradX) / det;
        
        // Apply reasonable bounds to the correction
        const maxDelta = 0.5;
        refinedX = peakX + Math.max(-maxDelta, Math.min(maxDelta, deltaX));
        refinedY = peakY + Math.max(-maxDelta, Math.min(maxDelta, deltaY));
      }
    }
    
    // Convert back to normalized coordinates
    const normalizedX = refinedX / (heatmapWidth - 1);
    const normalizedY = refinedY / (heatmapHeight - 1);
    
    // Clamp to valid range
    const finalX = Math.max(0, Math.min(1, normalizedX));
    const finalY = Math.max(0, Math.min(1, normalizedY));
    
    const refinedLandmark: Keypoint = {
      x: finalX,
      y: finalY,
      z: landmark.z,
      score: Math.max(landmark.score || 0, maxValue),
      name: landmark.name
    };
    
    refinedLandmarks.push(refinedLandmark);
    
    // Log first few refinements for debugging
    if (i < 5) {
      console.log(`🔧 HEATMAP REFINING: Landmark ${i}:`, {
        original: { x: landmark.x.toFixed(4), y: landmark.y.toFixed(4) },
        refined: { x: finalX.toFixed(4), y: finalY.toFixed(4) },
        heatmapPeak: { x: peakX, y: peakY, value: maxValue.toFixed(4) },
        confidence: refinedLandmark.score?.toFixed(4)
      });
    }
  }
  
  console.log(`✅ HEATMAP REFINING: Successfully refined ${refinedLandmarks.length} landmarks`);
  return refinedLandmarks;
}
