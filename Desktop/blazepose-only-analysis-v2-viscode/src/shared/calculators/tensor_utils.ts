
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';

/**
 * Safely disposes of tensors to prevent memory leaks.
 * Critical for maintaining performance in real-time applications.
 */
export function safeTensorDispose(tensor: tf.Tensor | tf.Tensor[] | null | undefined): void {
  if (!tensor) return;
  
  try {
    if (Array.isArray(tensor)) {
      tensor.forEach(t => {
        if (t && typeof t.dispose === 'function') {
          t.dispose();
        }
      });
    } else if (typeof tensor.dispose === 'function') {
      tensor.dispose();
    }
  } catch (error) {
    console.warn('🔧 TENSOR UTILS: Error disposing tensor:', error);
  }
}

/**
 * Validates tensor shapes and values for common issues.
 * Helps identify problems in the tensor processing pipeline.
 */
export function validateTensor(tensor: tf.Tensor, expectedShape?: number[]): boolean {
  try {
    if (!tensor || !tensor.shape) {
      console.warn('🔧 TENSOR VALIDATION: Invalid tensor - missing shape');
      return false;
    }
    
    // Check for expected shape if provided
    if (expectedShape && !arraysEqual(tensor.shape, expectedShape)) {
      console.warn('🔧 TENSOR VALIDATION: Shape mismatch:', {
        actual: tensor.shape,
        expected: expectedShape
      });
      return false;
    }
    
    // Check for NaN or infinite values
    const hasInvalidValues = tf.any(tf.logicalOr(
      tf.isNaN(tensor),
      tf.logicalNot(tf.isFinite(tensor))
    ));
    
    const hasInvalidValuesSync = hasInvalidValues.dataSync()[0];
    hasInvalidValues.dispose();
    
    if (hasInvalidValuesSync) {
      console.warn('🔧 TENSOR VALIDATION: Tensor contains NaN or infinite values');
      return false;
    }
    
    console.log('🔧 TENSOR VALIDATION: Tensor validation passed');
    return true;
    
  } catch (error) {
    console.error('🔧 TENSOR VALIDATION: Error validating tensor:', error);
    return false;
  }
}

/**
 * Safely extracts data from tensors with error handling.
 */
export async function safeTensorData(tensor: tf.Tensor): Promise<Float32Array | null> {
  try {
    if (!tensor) {
      console.warn('🔧 TENSOR UTILS: Cannot extract data from null tensor');
      return null;
    }
    
    const data = await tensor.data();
    console.log('🔧 TENSOR UTILS: Successfully extracted tensor data, length:', data.length);
    return new Float32Array(data);
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error extracting tensor data:', error);
    return null;
  }
}

/**
 * Converts keypoints to tensor format for processing.
 */
export function keypointsToTensor(keypoints: Keypoint[]): tf.Tensor2D {
  console.log('🔧 TENSOR UTILS: Converting keypoints to tensor');
  
  const data: number[][] = keypoints.map(kp => [
    kp.x || 0,
    kp.y || 0,
    kp.z || 0,
    kp.score || 0
  ]);
  
  const tensor = tf.tensor2d(data);
  console.log('🔧 TENSOR UTILS: Created keypoints tensor with shape:', tensor.shape);
  
  return tensor;
}

/**
 * Converts tensor back to keypoints array.
 */
export async function tensorToKeypoints(tensor: tf.Tensor2D, names?: string[]): Promise<Keypoint[]> {
  console.log('🔧 TENSOR UTILS: Converting tensor to keypoints');
  
  try {
    const data = await tensor.data();
    const [numKeypoints, dimensions] = tensor.shape;
    
    const keypoints: Keypoint[] = [];
    
    for (let i = 0; i < numKeypoints; i++) {
      const startIdx = i * dimensions;
      
      const keypoint: Keypoint = {
        x: data[startIdx] || 0,
        y: data[startIdx + 1] || 0,
        score: dimensions > 3 ? data[startIdx + 3] : undefined,
        name: names?.[i] || `point_${i}`
      };
      
      // Add z coordinate if available
      if (dimensions > 2) {
        keypoint.z = data[startIdx + 2] || 0;
      }
      
      keypoints.push(keypoint);
    }
    
    console.log('🔧 TENSOR UTILS: Converted tensor to', keypoints.length, 'keypoints');
    return keypoints;
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error converting tensor to keypoints:', error);
    return [];
  }
}

/**
 * Helper function to compare arrays for equality.
 */
function arraysEqual(a: number[], b: number[]): boolean {
  if (a.length !== b.length) return false;
  return a.every((val, index) => val === b[index]);
}

/**
 * Applies NaN filtering to tensor data.
 * Replaces NaN values with zeros or interpolated values.
 */
export function filterNaNValues(tensor: tf.Tensor): tf.Tensor {
  console.log('🔧 TENSOR UTILS: Filtering NaN values from tensor');
  
  try {
    // Replace NaN values with zeros
    const nanMask = tf.isNaN(tensor);
    const filteredTensor = tf.where(nanMask, tf.zerosLike(tensor), tensor);
    
    nanMask.dispose();
    
    console.log('🔧 TENSOR UTILS: NaN filtering complete');
    return filteredTensor;
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error filtering NaN values:', error);
    return tensor;
  }
}
