
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * Projects 3D world landmarks from ROI space to world coordinate system.
 * This handles the 3D coordinate transformation for world landmarks.
 */
export function calculateWorldLandmarkProjection(
    worldLandmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 WORLD PROJECTION: Projecting 3D world landmarks');
  console.log('🔧 WORLD PROJECTION: ROI rect:', rect);
  console.log('🔧 WORLD PROJECTION: Input world landmarks count:', worldLandmarks.length);

  const projectedWorldLandmarks: Keypoint[] = worldLandmarks.map((landmark, index) => {
    // PHASE 6B: NaN-safe world coordinate handling
    let x = landmark.x || 0;
    let y = landmark.y || 0;
    let z = landmark.z || 0;
    
    // PHASE 6B: Validate input world coordinates
    if (isNaN(x) || !isFinite(x)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN world X in projection ${index}`);
      x = 0;
    }
    if (isNaN(y) || !isFinite(y)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN world Y in projection ${index}`);
      y = 0;
    }
    if (isNaN(z) || !isFinite(z)) {
      z = 0;
    }
    
    // For world landmarks, we primarily need to handle scaling
    // The world coordinate system is relative to the detected pose
    const projectedLandmark: Keypoint = {
      x: x,
      y: y,
      z: z,
      score: landmark.score,
      name: landmark.name
    };

    // PHASE 6B: Safe scaling with NaN protection
    const rectWidth = rect.width || 1;
    const rectHeight = rect.height || 1;
    const scaleFactor = Math.sqrt(rectWidth * rectHeight);
    
    if (isFinite(scaleFactor) && scaleFactor > 0) {
      const scaledX = projectedLandmark.x * scaleFactor;
      const scaledY = projectedLandmark.y * scaleFactor;
      const scaledZ = projectedLandmark.z * scaleFactor;
      
      // PHASE 6B: Validate scaled coordinates
      projectedLandmark.x = isFinite(scaledX) ? scaledX : projectedLandmark.x;
      projectedLandmark.y = isFinite(scaledY) ? scaledY : projectedLandmark.y;
      projectedLandmark.z = isFinite(scaledZ) ? scaledZ : projectedLandmark.z;
    } else {
      console.warn(`🔧 PHASE 6B: Invalid scale factor for world landmark ${index}`);
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 PHASE 6B: World landmark ${index} projection:`, {
        original: { 
          x: landmark.x?.toFixed(4), 
          y: landmark.y?.toFixed(4), 
          z: landmark.z?.toFixed(4) 
        },
        projected: { 
          x: projectedLandmark.x?.toFixed(4), 
          y: projectedLandmark.y?.toFixed(4), 
          z: projectedLandmark.z?.toFixed(4) 
        },
        scaleFactor: scaleFactor.toFixed(4),
        score: landmark.score?.toFixed(4)
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 WORLD PROJECTION: Successfully projected ${projectedWorldLandmarks.length} world landmarks`);
  return projectedWorldLandmarks;
}
