
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * PHASE 1: ROI (Region of Interest) processing utilities for BlazePose.
 */

/**
 * Calculates ROI from detected landmarks.
 */
export function calculateROIFromLandmarks(
    landmarks: Keypoint[], 
    imageSize: ImageSize, 
    expansionFactor: number = 1.3): Rect {
  
  console.log('🔧 ROI PROCESSING: Calculating ROI from landmarks');
  
  if (landmarks.length === 0) {
    return {
      xCenter: imageSize.width / 2,
      yCenter: imageSize.height / 2,
      width: imageSize.width,
      height: imageSize.height,
      rotation: 0
    };
  }

  // Find bounding box of all valid landmarks
  const validLandmarks = landmarks.filter(lm => lm.score && lm.score > 0.1);
  
  if (validLandmarks.length === 0) {
    return {
      xCenter: imageSize.width / 2,
      yCenter: imageSize.height / 2,
      width: imageSize.width,
      height: imageSize.height,
      rotation: 0
    };
  }

  let minX = validLandmarks[0].x;
  let maxX = validLandmarks[0].x;
  let minY = validLandmarks[0].y;
  let maxY = validLandmarks[0].y;

  validLandmarks.forEach(landmark => {
    minX = Math.min(minX, landmark.x);
    maxX = Math.max(maxX, landmark.x);
    minY = Math.min(minY, landmark.y);
    maxY = Math.max(maxY, landmark.y);
  });

  const width = (maxX - minX) * expansionFactor;
  const height = (maxY - minY) * expansionFactor;
  const xCenter = (minX + maxX) / 2;
  const yCenter = (minY + maxY) / 2;

  const roi: Rect = {
    xCenter,
    yCenter,
    width: Math.max(width, height), // Make it square
    height: Math.max(width, height),
    rotation: 0
  };

  console.log('🔧 ROI PROCESSING: Calculated ROI:', roi);
  return roi;
}

/**
 * Expands ROI by a given factor while keeping it within image bounds.
 */
export function expandROI(roi: Rect, factor: number, imageSize: ImageSize): Rect {
  const newWidth = roi.width * factor;
  const newHeight = roi.height * factor;
  
  return {
    xCenter: roi.xCenter,
    yCenter: roi.yCenter,
    width: Math.min(newWidth, imageSize.width),
    height: Math.min(newHeight, imageSize.height),
    rotation: roi.rotation || 0
  };
}

/**
 * Validates ROI bounds against image size.
 */
export function validateROI(roi: Rect, imageSize: ImageSize): boolean {
  const halfWidth = roi.width / 2;
  const halfHeight = roi.height / 2;
  
  const left = roi.xCenter - halfWidth;
  const right = roi.xCenter + halfWidth;
  const top = roi.yCenter - halfHeight;
  const bottom = roi.yCenter + halfHeight;
  
  return left >= 0 && right <= imageSize.width && 
         top >= 0 && bottom <= imageSize.height;
}

/**
 * ROI smoothing filter for temporal stability.
 */
export class ROISmoothingFilter {
  private previousROI?: Rect;
  private readonly alpha: number;

  constructor(alpha: number = 0.3) {
    this.alpha = alpha;
  }

  apply(roi: Rect): Rect {
    if (!this.previousROI) {
      this.previousROI = { ...roi };
      return roi;
    }

    const smoothedROI: Rect = {
      xCenter: this.alpha * roi.xCenter + (1 - this.alpha) * this.previousROI.xCenter,
      yCenter: this.alpha * roi.yCenter + (1 - this.alpha) * this.previousROI.yCenter,
      width: this.alpha * roi.width + (1 - this.alpha) * this.previousROI.width,
      height: this.alpha * roi.height + (1 - this.alpha) * this.previousROI.height,
      rotation: roi.rotation || 0
    };

    this.previousROI = { ...smoothedROI };
    return smoothedROI;
  }

  reset(): void {
    this.previousROI = undefined;
  }
}
