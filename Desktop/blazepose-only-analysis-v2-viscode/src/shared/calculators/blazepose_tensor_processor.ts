
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { safeTensorDispose, validateTensor, filterNaNValues } from './tensor_utils';

/**
 * Phase 3: Enhanced BlazePose tensor processing with improved coordinate handling
 */

/**
 * Validates and cleans pose coordinates with enhanced error handling.
 * This is the critical function that was causing widespread NaN issues.
 */
export function validateAndCleanPoseCoordinates(
    landmarks: Keypoint[],
    worldLandmarks: Keypoint[]): { landmarks: Keypoint[], worldLandmarks: Keypoint[] } {
  
  console.log('🔍 PHASE 6G: RAW TENSOR DATA - Starting coordinate validation');
  console.log('🔍 PHASE 6G: RAW TENSOR DATA - Input counts:', { landmarks: landmarks.length, worldLandmarks: worldLandmarks.length });
  
  // PHASE 6G: Inspect raw tensor data BEFORE any processing
  console.log('🔍 PHASE 6G: RAW TENSOR DATA - First 10 landmarks (RAW):');
  landmarks.slice(0, 10).forEach((kp, idx) => {
    console.log(`🔍 PHASE 6G: RAW ${idx}:`, {
      x: kp.x,
      y: kp.y,
      z: kp.z,
      score: kp.score,
      name: kp.name || 'unnamed',
      isNaN_x: isNaN(kp.x),
      isNaN_y: isNaN(kp.y),
      isFinite_x: isFinite(kp.x),
      isFinite_y: isFinite(kp.y)
    });
  });
  
  console.log('🔍 PHASE 6G: RAW TENSOR DATA - First 5 world landmarks (RAW):');
  worldLandmarks.slice(0, 5).forEach((kp, idx) => {
    console.log(`🔍 PHASE 6G: RAW 3D ${idx}:`, {
      x: kp.x,
      y: kp.y, 
      z: kp.z,
      score: kp.score,
      name: kp.name || 'unnamed',
      isNaN_x: isNaN(kp.x),
      isNaN_y: isNaN(kp.y),
      isNaN_z: isNaN(kp.z)
    });
  });
  
  // Enhanced validation counters
  let fixedLandmarks = 0;
  let fixedWorldLandmarks = 0;
  
  const cleanedLandmarks = landmarks.map((landmark, index) => {
    const cleanedLandmark = { ...landmark };
    let wasFixed = false;
    
    // Enhanced coordinate validation with better defaults
    if (!isValidCoordinate(cleanedLandmark.x)) {
      cleanedLandmark.x = 0.5; // Center default instead of 0
      wasFixed = true;
    }
    
    if (!isValidCoordinate(cleanedLandmark.y)) {
      cleanedLandmark.y = 0.5; // Center default instead of 0
      wasFixed = true;
    }
    
    if (cleanedLandmark.z !== undefined && !isValidCoordinate(cleanedLandmark.z)) {
      cleanedLandmark.z = 0;
      wasFixed = true;
    }
    
    // Enhanced score validation
    if (cleanedLandmark.score !== undefined && !isValidScore(cleanedLandmark.score)) {
      cleanedLandmark.score = 0.1; // Low confidence default
      wasFixed = true;
    }
    
    if (wasFixed) {
      fixedLandmarks++;
      // Only log first few fixes to avoid spam
      if (fixedLandmarks <= 5) {
        console.log(`🔧 PHASE 3 ENHANCED: Fixed landmark ${index} coordinates`);
      }
    }
    
    return cleanedLandmark;
  });
  
  const cleanedWorldLandmarks = worldLandmarks.map((landmark, index) => {
    const cleanedLandmark = { ...landmark };
    let wasFixed = false;
    
    // World coordinates use different validation (can be negative)
    if (!isValidWorldCoordinate(cleanedLandmark.x)) {
      cleanedLandmark.x = 0;
      wasFixed = true;
    }
    
    if (!isValidWorldCoordinate(cleanedLandmark.y)) {
      cleanedLandmark.y = 0;
      wasFixed = true;
    }
    
    if (cleanedLandmark.z !== undefined && !isValidWorldCoordinate(cleanedLandmark.z)) {
      cleanedLandmark.z = 0;
      wasFixed = true;
    }
    
    if (cleanedLandmark.score !== undefined && !isValidScore(cleanedLandmark.score)) {
      cleanedLandmark.score = 0.1;
      wasFixed = true;
    }
    
    if (wasFixed) {
      fixedWorldLandmarks++;
      // Only log first few fixes to avoid spam
      if (fixedWorldLandmarks <= 5) {
        console.log(`🔧 PHASE 3 ENHANCED: Fixed world landmark ${index} coordinates`);
      }
    }
    
    return cleanedLandmark;
  });
  
  // Summary logging instead of individual coordinate fixes
  if (fixedLandmarks > 0 || fixedWorldLandmarks > 0) {
    console.log('🔧 PHASE 3 ENHANCED: Coordinate validation summary:', {
      fixedLandmarks: fixedLandmarks,
      fixedWorldLandmarks: fixedWorldLandmarks,
      totalLandmarks: landmarks.length,
      totalWorldLandmarks: worldLandmarks.length,
      successRate: `${Math.round(((landmarks.length - fixedLandmarks) / landmarks.length) * 100)}%`
    });
  } else {
    console.log('✅ PHASE 3 ENHANCED: All coordinates valid, no fixes needed');
  }
  
  return {
    landmarks: cleanedLandmarks,
    worldLandmarks: cleanedWorldLandmarks
  };
}

/**
 * Enhanced coordinate validation functions
 */
function isValidCoordinate(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         value >= 0 && 
         value <= 1; // Normalized coordinates should be 0-1
}

function isValidWorldCoordinate(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         Math.abs(value) < 10; // Reasonable world coordinate bounds
}

function isValidScore(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         value >= 0 && 
         value <= 1;
}

/**
 * Processes raw pose detection tensors with enhanced error handling.
 */
export async function processPoseDetectionTensors(
    poseOutputs: tf.Tensor[],
    imageSize: ImageSize): Promise<{ landmarks: Keypoint[], worldLandmarks: Keypoint[] }> {
  
  console.log('🔧 PHASE 3 ENHANCED: Processing BlazePose tensors with enhanced handling');
  console.log('🔧 PHASE 3 ENHANCED: Input tensor count:', poseOutputs.length);
  
  try {
    let landmarks: Keypoint[] = [];
    let worldLandmarks: Keypoint[] = [];
    
    // Process each tensor with enhanced validation
    for (let i = 0; i < poseOutputs.length; i++) {
      const tensor = poseOutputs[i];
      
      if (!validateTensor(tensor)) {
        console.warn(`🔧 PHASE 3 ENHANCED: Skipping invalid tensor ${i}`);
        continue;
      }
      
      console.log(`🔧 PHASE 3 ENHANCED: Processing tensor ${i} shape:`, tensor.shape);
      
      // Enhanced tensor filtering
      const filteredTensor = filterNaNValues(tensor);
      
      // Determine tensor type and process accordingly
      if (isMayKnowPoseLandmarksTensor(tensor)) {
        const processedLandmarks = await processPoseLandmarksTensorEnhanced(filteredTensor, imageSize);
        if (processedLandmarks.length > 0) {
          landmarks = processedLandmarks;
          console.log('✅ PHASE 3 ENHANCED: Extracted enhanced pose landmarks');
        }
      } else if (isMayKnowWorldLandmarksTensor(tensor)) {
        const processedWorldLandmarks = await processWorldLandmarksTensorEnhanced(filteredTensor);
        if (processedWorldLandmarks.length > 0) {
          worldLandmarks = processedWorldLandmarks;
          console.log('✅ PHASE 3 ENHANCED: Extracted enhanced world landmarks');
        }
      }
      
      safeTensorDispose(filteredTensor);
    }
    
    console.log('✅ PHASE 3 ENHANCED: Tensor processing complete:', {
      landmarks: landmarks.length,
      worldLandmarks: worldLandmarks.length
    });
    
    return { landmarks, worldLandmarks };
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Tensor processing error:', error);
    return { landmarks: [], worldLandmarks: [] };
  }
}

/**
 * Enhanced tensor type detection
 */
function isMayKnowPoseLandmarksTensor(tensor: tf.Tensor): boolean {
  const shape = tensor.shape;
  return shape.length >= 2 && shape[shape.length - 1] >= 3;
}

function isMayKnowWorldLandmarksTensor(tensor: tf.Tensor): boolean {
  const shape = tensor.shape;
  return shape.length >= 2 && shape[shape.length - 1] >= 3;
}

/**
 * Enhanced pose landmarks processing
 */
async function processPoseLandmarksTensorEnhanced(
    tensor: tf.Tensor,
    imageSize: ImageSize): Promise<Keypoint[]> {
  
  console.log('🔧 PHASE 3 ENHANCED: Processing pose landmarks with enhanced validation');
  
  try {
    const data = await tensor.data();
    const shape = tensor.shape;
    
    // Handle different tensor shapes
    let numLandmarks: number;
    let coordsPerLandmark: number;
    
    if (shape.length === 3) {
      // Shape: [batch, landmarks, coords]
      numLandmarks = shape[1];
      coordsPerLandmark = shape[2];
    } else if (shape.length === 2) {
      // Shape: [landmarks, coords]
      numLandmarks = shape[0];
      coordsPerLandmark = shape[1];
    } else {
      console.warn('🔧 PHASE 3 ENHANCED: Unsupported tensor shape for landmarks');
      return [];
    }
    
    console.log('🔧 PHASE 3 ENHANCED: Tensor analysis:', {
      numLandmarks,
      coordsPerLandmark,
      dataLength: data.length
    });
    
    const landmarks: Keypoint[] = [];
    
    for (let i = 0; i < numLandmarks; i++) {
      const baseIndex = i * coordsPerLandmark;
      
      // PHASE 6D: Enhanced coordinate extraction with detailed debugging
      let rawX = data[baseIndex];
      let rawY = data[baseIndex + 1];
      let rawZ = coordsPerLandmark > 2 ? data[baseIndex + 2] : 0;
      let rawScore = coordsPerLandmark > 3 ? data[baseIndex + 3] : 0.5;
      
      // PHASE 6G: DEEPEST tensor debugging - log everything about raw data
      if (i < 10) {
        console.log(`🔍 PHASE 6G: RAW TENSOR DATA for landmark ${i}:`, {
          baseIndex,
          coordsPerLandmark,
          rawValues: {
            rawX: rawX,
            rawY: rawY,
            rawZ: rawZ,
            rawScore: rawScore
          },
          dataTypes: {
            xType: typeof rawX,
            yType: typeof rawY,
            zType: typeof rawZ,
            scoreType: typeof rawScore
          },
          validationChecks: {
            isXNaN: isNaN(rawX),
            isYNaN: isNaN(rawY),
            isXFinite: isFinite(rawX),
            isYFinite: isFinite(rawY),
            xInRange: rawX >= 0 && rawX <= 1,
            yInRange: rawY >= 0 && rawY <= 1
          },
          rawDataSlice: Array.from(data.slice(baseIndex, baseIndex + Math.min(coordsPerLandmark, 10))),
          tensorMetadata: {
            tensorShape: shape,
            totalDataLength: data.length,
            expectedLength: numLandmarks * coordsPerLandmark
          }
        });
      }
      
      // PHASE 6E: Log tensor shape and data structure
      if (i === 0) {
        console.log(`🔍 PHASE 6E: Tensor structure analysis:`, {
          tensorShape: shape,
          numLandmarks,
          coordsPerLandmark,
          totalDataLength: data.length,
          expectedDataLength: numLandmarks * coordsPerLandmark,
          firstTenValues: Array.from(data.slice(0, 10))
        });
      }
      
      // PHASE 6B: NaN validation and replacement
      if (isNaN(rawX) || !isFinite(rawX)) {
        console.warn(`🔧 PHASE 6B: Fixed NaN X coordinate at landmark ${i}`);
        rawX = 0.5;
      }
      if (isNaN(rawY) || !isFinite(rawY)) {
        console.warn(`🔧 PHASE 6B: Fixed NaN Y coordinate at landmark ${i}`);
        rawY = 0.5;
      }
      if (isNaN(rawZ) || !isFinite(rawZ)) {
        rawZ = 0;
      }
      if (isNaN(rawScore) || !isFinite(rawScore)) {
        rawScore = 0.5;
      }
      
      // Enhanced coordinate normalization
      let x = rawX;
      let y = rawY;
      
      // Check if coordinates need normalization
      if (x > 1.0 || y > 1.0) {
        x = x / imageSize.width;
        y = y / imageSize.height;
      }
      
      // PHASE 6E: TEMPORARILY DISABLE validation to see raw coordinates
      const finalX = x; // isValidCoordinate(x) ? x : 0.5;
      const finalY = y; // isValidCoordinate(y) ? y : 0.5;
      const finalZ = rawZ; // isValidCoordinate(rawZ) ? rawZ : 0;
      const finalScore = rawScore; // isValidScore(rawScore) ? rawScore : 0.5;
      
      landmarks.push({
        x: finalX,
        y: finalY,
        z: finalZ,
        score: finalScore,
        name: `landmark_${i}`
      });
      
      // PHASE 6B: Log first few landmarks for debugging
      if (i < 3) {
        console.log(`🔧 PHASE 6B: Landmark ${i} processed:`, {
          raw: { x: rawX.toFixed(4), y: rawY.toFixed(4), score: rawScore.toFixed(4) },
          final: { x: finalX.toFixed(4), y: finalY.toFixed(4), score: finalScore.toFixed(4) }
        });
      }
    }
    
    console.log('✅ PHASE 3 ENHANCED: Processed enhanced landmarks:', landmarks.length);
    return landmarks;
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Error processing landmarks tensor:', error);
    return [];
  }
}

/**
 * Enhanced world landmarks processing
 */
async function processWorldLandmarksTensorEnhanced(tensor: tf.Tensor): Promise<Keypoint[]> {
  console.log('🔧 PHASE 3 ENHANCED: Processing world landmarks with enhanced validation');
  
  try {
    const data = await tensor.data();
    const shape = tensor.shape;
    
    // Handle different tensor shapes
    let numLandmarks: number;
    let coordsPerLandmark: number;
    
    if (shape.length === 3) {
      numLandmarks = shape[1];
      coordsPerLandmark = shape[2];
    } else if (shape.length === 2) {
      numLandmarks = shape[0];
      coordsPerLandmark = shape[1];
    } else {
      console.warn('🔧 PHASE 3 ENHANCED: Unsupported tensor shape for world landmarks');
      return [];
    }
    
    const worldLandmarks: Keypoint[] = [];
    
    for (let i = 0; i < numLandmarks; i++) {
      const baseIndex = i * coordsPerLandmark;
      
      // PHASE 6B: Enhanced NaN-safe world coordinate extraction
      let rawX = data[baseIndex];
      let rawY = data[baseIndex + 1];
      let rawZ = coordsPerLandmark > 2 ? data[baseIndex + 2] : 0;
      let rawScore = coordsPerLandmark > 3 ? data[baseIndex + 3] : 0.5;
      
      // PHASE 6B: NaN validation and replacement for world coordinates
      if (isNaN(rawX) || !isFinite(rawX)) {
        console.warn(`🔧 PHASE 6B: Fixed NaN world X coordinate at landmark ${i}`);
        rawX = 0;
      }
      if (isNaN(rawY) || !isFinite(rawY)) {
        console.warn(`🔧 PHASE 6B: Fixed NaN world Y coordinate at landmark ${i}`);
        rawY = 0;
      }
      if (isNaN(rawZ) || !isFinite(rawZ)) {
        rawZ = 0;
      }
      if (isNaN(rawScore) || !isFinite(rawScore)) {
        rawScore = 0.5;
      }
      
      // PHASE 6B: Final validation with enhanced bounds checking
      const finalX = isValidWorldCoordinate(rawX) ? rawX : 0;
      const finalY = isValidWorldCoordinate(rawY) ? rawY : 0;
      const finalZ = isValidWorldCoordinate(rawZ) ? rawZ : 0;
      const finalScore = isValidScore(rawScore) ? rawScore : 0.5;
      
      worldLandmarks.push({
        x: finalX,
        y: finalY,
        z: finalZ,
        score: finalScore,
        name: `world_landmark_${i}`
      });
      
      // PHASE 6B: Log first few world landmarks for debugging
      if (i < 3) {
        console.log(`🔧 PHASE 6B: World landmark ${i} processed:`, {
          raw: { x: rawX.toFixed(4), y: rawY.toFixed(4), z: rawZ.toFixed(4) },
          final: { x: finalX.toFixed(4), y: finalY.toFixed(4), z: finalZ.toFixed(4) }
        });
      }
    }
    
    console.log('✅ PHASE 3 ENHANCED: Processed enhanced world landmarks:', worldLandmarks.length);
    return worldLandmarks;
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Error processing world landmarks tensor:', error);
    return [];
  }
}
