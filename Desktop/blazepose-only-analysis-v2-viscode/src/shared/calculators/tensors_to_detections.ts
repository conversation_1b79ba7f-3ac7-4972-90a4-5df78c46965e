
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Detection } from './interfaces/common_interfaces';
import { Anchor } from './create_ssd_anchors';

export interface TensorsToDetectionsConfig {
  numClasses: number;
  numBoxes: number;
  numCoords: number;
  boxCoordOffset: number;
  keypointCoordOffset: number;
  numKeypoints: number;
  numValuesPerKeypoint: number;
  sigmoidScore: boolean;
  scoreClippingThresh: number;
  reverseOutputOrder: boolean;
  xScale: number;
  yScale: number;
  wScale: number;
  hScale: number;
  minScoreThresh: number;
}

/**
 * PHASE 1: Converts raw detection tensors to detection objects.
 */
export async function tensorsToDetections(
    detectionTensor: tf.Tensor,
    anchors: Anchor[],
    config: TensorsToDetectionsConfig): Promise<Detection[]> {
  
  // Reduced logging for performance
  console.log('🔧 TENSORS TO DETECTIONS: Starting conversion...');

  const detections: Detection[] = [];
  
  if (!detectionTensor || anchors.length === 0) {
    console.warn('🔧 TENSORS TO DETECTIONS: No tensor or anchors provided');
    return detections;
  }

  // Validate tensor dimensions
  if (detectionTensor.shape.length !== 3) {
    console.error('🔧 TENSORS TO DETECTIONS: Expected 3D tensor, got shape:', detectionTensor.shape);
    return detections;
  }

  const [batchSize, numBoxes, numCoords] = detectionTensor.shape;
  // Tensor dimensions logged only if needed

  // TASK 1: BlazePose-specific tensor shape validation
  if (batchSize !== 1) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects batch size 1, got:', batchSize);
    return detections;
  }

  if (numCoords !== 13) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects 13 coordinates per box, got:', numCoords);
    console.error('🔧 TENSORS TO DETECTIONS: Expected format: [yCenter, xCenter, height, width, score, ...8 additional values]');
    return detections;
  }

  // Validation passed

  try {
    // TASK 5: Comprehensive tensor debugging starts here
    const startTime = performance.now();
    // Starting tensor data extraction
    
    // Tensor metadata logging disabled for performance
    
    // Get tensor data as Float32Array for proper indexing
    const tensorData = await detectionTensor.data();
    const extractionTime = performance.now() - startTime;
    // Extraction completed
    
    // Validate tensor data extraction
    if (!tensorData || tensorData.length === 0) {
      console.error('🔧 TENSORS TO DETECTIONS: Tensor data extraction failed - empty or null data');
      return detections;
    }
    
    const expectedDataLength = batchSize * numBoxes * numCoords;
    if (tensorData.length !== expectedDataLength) {
      console.error(`🔧 TENSORS TO DETECTIONS: Tensor data length mismatch. Expected: ${expectedDataLength}, Got: ${tensorData.length}`);
      return detections;
    }
    
    // Tensor data extracted successfully
    
    // TASK 5: Statistical analysis of tensor data
    const tensorDataArray = Array.from(tensorData);
    const tensorStats = {
      min: Math.min(...tensorDataArray),
      max: Math.max(...tensorDataArray),
      mean: tensorDataArray.reduce((sum, val) => sum + val, 0) / tensorDataArray.length,
      nonZeroCount: tensorDataArray.filter(v => v !== 0).length,
      infiniteCount: tensorDataArray.filter(v => !isFinite(v)).length
    };
    
    // Tensor statistics disabled for performance
    
    // Calculate number of detections to process
    const numDetections = Math.min(numBoxes, anchors.length);
    // Processing detections
    
    // TASK 5: Log anchor information summary
    const anchorStats = {
      total: anchors.length,
      validAnchors: 0,
      avgWidth: 0,
      avgHeight: 0,
      minWidth: Number.MAX_VALUE,
      maxWidth: Number.MIN_VALUE,
      minHeight: Number.MAX_VALUE,
      maxHeight: Number.MIN_VALUE
    };
    
    let validAnchorSum = { width: 0, height: 0 };
    anchors.slice(0, numDetections).forEach((anchor, i) => {
      if (anchor && typeof anchor.width === 'number' && typeof anchor.height === 'number' && 
          anchor.width > 0 && anchor.height > 0) {
        anchorStats.validAnchors++;
        validAnchorSum.width += anchor.width;
        validAnchorSum.height += anchor.height;
        anchorStats.minWidth = Math.min(anchorStats.minWidth, anchor.width);
        anchorStats.maxWidth = Math.max(anchorStats.maxWidth, anchor.width);
        anchorStats.minHeight = Math.min(anchorStats.minHeight, anchor.height);
        anchorStats.maxHeight = Math.max(anchorStats.maxHeight, anchor.height);
      }
    });
    
    if (anchorStats.validAnchors > 0) {
      anchorStats.avgWidth = validAnchorSum.width / anchorStats.validAnchors;
      anchorStats.avgHeight = validAnchorSum.height / anchorStats.validAnchors;
    }
    
    // Anchor statistics and sample data logging disabled for performance

    for (let i = 0; i < numDetections; i++) {
      const anchor = anchors[i];
      
      // TASK 3: Validate anchor data integrity
      if (!anchor || typeof anchor.xCenter !== 'number' || typeof anchor.yCenter !== 'number' ||
          typeof anchor.width !== 'number' || typeof anchor.height !== 'number') {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at index ${i}:`, anchor);
        continue;
      }

      if (anchor.width <= 0 || anchor.height <= 0) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor dimensions at index ${i}: width=${anchor.width}, height=${anchor.height}`);
        continue;
      }
      
      // Correct 3D tensor indexing: batch=0, detection=i, values=0-12
      // Formula: tensorData[batch * numBoxes * numCoords + detection * numCoords + valueIndex]
      const baseIndex = 0 * numBoxes * numCoords + i * numCoords;
      
      // TASK 4: Bounds checking for tensor access
      if (baseIndex < 0 || baseIndex + 12 >= tensorData.length) {
        console.error(`🔧 TENSORS TO DETECTIONS: Tensor access out of bounds at detection ${i}. BaseIndex: ${baseIndex}, TensorLength: ${tensorData.length}`);
        continue;
      }
      
      // Extract box coordinates (first 4 values)
      // BlazePose format: [yCenter, xCenter, height, width, score, ...]
      const yCenter = tensorData[baseIndex + 0];
      const xCenter = tensorData[baseIndex + 1]; 
      const h = tensorData[baseIndex + 2];
      const w = tensorData[baseIndex + 3];

      // Detailed detection logging disabled for performance

      // Validate extracted coordinates
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w)) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid coordinates at detection ${i}: yCenter=${yCenter}, xCenter=${xCenter}, h=${h}, w=${w}`);
        continue;
      }
      
      // Extract and process score (5th value, index 4)
      let score = tensorData[baseIndex + 4];
      if (config.sigmoidScore) {
        score = 1.0 / (1.0 + Math.exp(-score));
      }

      // Validate score
      if (!isFinite(score) || score < 0 || score > 1) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid score at detection ${i}: ${score}`);
        continue;
      }

      // Apply score threshold early
      if (score < config.minScoreThresh) {
        continue;
      }

      // TASK 3: FIXED - BlazePose coordinate transformation with proper scaling
      // BlazePose outputs normalized coordinates that need proper scaling
      
      // Validate input values first
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w)) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid raw coordinates at detection ${i}:`, {
          yCenter, xCenter, h, w
        });
        continue;
      }
      
      // Validate anchor values
      if (!isFinite(anchor.xCenter) || !isFinite(anchor.yCenter) || 
          !isFinite(anchor.width) || !isFinite(anchor.height) ||
          anchor.width <= 0 || anchor.height <= 0) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at detection ${i}:`, anchor);
        continue;
      }
      
      // FIXED: Use correct BlazePose coordinate transformation without scaling issues
      // BlazePose coordinates are already normalized relative to the input image
      let anchorCenterX, anchorCenterY, boxWidth, boxHeight;
      
      try {
        // FINAL FIX: Use proper BlazePose coordinate transformation
        console.log(`🔧 TRANSFORM DEBUG ${i}: Raw values`, { yCenter, xCenter, h, w, anchor });
        
        // BlazePose outputs coordinates in a specific format - use minimal scaling
        // The raw values are already close to the expected range
        const scaleX = 0.01; // Very small scale for center offset
        const scaleY = 0.01; // Very small scale for center offset  
        const sizeScaleW = 0.05; // Small scale for width
        const sizeScaleH = 0.05; // Small scale for height
        
        anchorCenterX = anchor.xCenter + (xCenter * scaleX);
        anchorCenterY = anchor.yCenter + (yCenter * scaleY); 
        boxWidth = Math.max(0.001, anchor.width * Math.abs(w) * sizeScaleW);
        boxHeight = Math.max(0.001, anchor.height * Math.abs(h) * sizeScaleH);
        
        // Minimal debug logging to reduce bandwidth
        if (i % 500 === 0) { // Only log every 500th detection
          console.log(`🔧 TRANSFORM ${i}: Processing batch...`);
        }
        
        // Additional safety checks
        if (!isFinite(anchorCenterX)) anchorCenterX = anchor.xCenter;
        if (!isFinite(anchorCenterY)) anchorCenterY = anchor.yCenter;
        if (!isFinite(boxWidth) || boxWidth <= 0) boxWidth = anchor.width * 0.1;
        if (!isFinite(boxHeight) || boxHeight <= 0) boxHeight = anchor.height * 0.1;
        
      } catch (transformError) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Transform error at detection ${i}:`, transformError);
        // Use safe fallback values
        anchorCenterX = anchor.xCenter;
        anchorCenterY = anchor.yCenter;
        boxWidth = anchor.width * 0.1;
        boxHeight = anchor.height * 0.1;
      }

      // MAXIMUM PERMISSIVE validation - for full-frame runner (5ft at 5ft distance)
      // Runner can span entire video width/height, so allow extreme coordinate ranges
      if (!isFinite(anchorCenterX) || !isFinite(anchorCenterY) ||
          !isFinite(boxWidth) || !isFinite(boxHeight) ||
          boxWidth <= 0 || boxHeight <= 0 ||
          Math.abs(anchorCenterX) > 50.0 || Math.abs(anchorCenterY) > 5000.0 ||
          boxWidth > 10.0 || boxHeight > 10.0) {
        // Only log extreme rejections to reduce bandwidth
        if (Math.abs(anchorCenterY) > 10000 || boxWidth > 20 || boxHeight > 20) {
          console.warn(`🔧 REJECT DETECTION ${i}: Extreme bounds`, {
            anchorCenterX: anchorCenterX?.toFixed(2) || 'NaN',
            anchorCenterY: anchorCenterY?.toFixed(2) || 'NaN',
            boxWidth: boxWidth?.toFixed(2) || 'NaN',
            boxHeight: boxHeight?.toFixed(2) || 'NaN'
          });
        }
        continue;
      }

      // Calculate bounding box corners
      const xMin = anchorCenterX - boxWidth / 2;
      const yMin = anchorCenterY - boxHeight / 2;
      const xMax = anchorCenterX + boxWidth / 2;
      const yMax = anchorCenterY + boxHeight / 2;

      // Ensure coordinates are normalized (0-1 range) and positive
      const normalizedXMin = Math.max(0, Math.min(1, xMin));
      const normalizedYMin = Math.max(0, Math.min(1, yMin));
      const normalizedXMax = Math.max(0, Math.min(1, xMax));
      const normalizedYMax = Math.max(0, Math.min(1, yMax));
      const normalizedWidth = normalizedXMax - normalizedXMin;
      const normalizedHeight = normalizedYMax - normalizedYMin;

      // SAFE detection object creation with validation
      // Create relativeBoundingBox for removeDetectionLetterbox compatibility
      const relativeBoundingBoxForLetterbox = {
        xMin: Math.max(0, Math.min(1, normalizedXMin)),
        yMin: Math.max(0, Math.min(1, normalizedYMin)),
        xMax: Math.max(0, Math.min(1, normalizedXMax)),
        yMax: Math.max(0, Math.min(1, normalizedYMax)),
        width: Math.max(0.001, Math.min(1, normalizedWidth)),
        height: Math.max(0.001, Math.min(1, normalizedHeight))
      };

      const detection: Detection = {
        boundingBox: {
          xMin: Math.max(0, Math.min(1, normalizedXMin)),
          yMin: Math.max(0, Math.min(1, normalizedYMin)),
          xMax: Math.max(0, Math.min(1, normalizedXMax)),
          yMax: Math.max(0, Math.min(1, normalizedYMax)),
          width: Math.max(0.001, Math.min(1, normalizedWidth)),
          height: Math.max(0.001, Math.min(1, normalizedHeight))
        },
        locationData: {
          format: 'RELATIVE_BOUNDING_BOX',
          relativeBoundingBox: relativeBoundingBoxForLetterbox,
          // Add relativeKeypoints for calculateAlignmentPointsRects compatibility
          relativeKeypoints: [
            // Keypoint 0: Top-left corner (start keypoint for alignment)
            { x: Math.max(0, Math.min(1, normalizedXMin)), y: Math.max(0, Math.min(1, normalizedYMin)) },
            // Keypoint 1: Bottom-right corner (end keypoint for alignment)
            { x: Math.max(0, Math.min(1, normalizedXMax)), y: Math.max(0, Math.min(1, normalizedYMax)) }
          ]
        },
        relativeBoundingBox: {
          xCenter: Math.max(-50, Math.min(50, anchorCenterX)),
          yCenter: Math.max(-5000, Math.min(5000, anchorCenterY)),
          width: Math.max(0.001, Math.min(10, boxWidth)),
          height: Math.max(0.001, Math.min(10, boxHeight)),
          rotation: 0
        },
        score: Math.max(0, Math.min(1, score))
      };
      
      console.log(`🔧 CREATED DETECTION ${i}:`, {
        score: detection.score.toFixed(3),
        bbox: detection.boundingBox,
        relBbox: detection.relativeBoundingBox
      });

      detections.push(detection);
    }

    // TASK 5: Processing summary and performance metrics
    const totalProcessingTime = performance.now() - startTime;
    const processingStats = {
      totalTime: `${totalProcessingTime.toFixed(2)}ms`,
      extractionTime: `${extractionTime.toFixed(2)}ms`,
      processingTime: `${(totalProcessingTime - extractionTime).toFixed(2)}ms`,
      detectionsProcessed: numDetections,
      validDetections: detections.length,
      successRate: `${(detections.length / numDetections * 100).toFixed(1)}%`,
      avgTimePerDetection: `${((totalProcessingTime - extractionTime) / numDetections).toFixed(3)}ms`
    };
    
    // Final summary: Created ${detections.length} valid detections from ${numDetections} processed
    console.log(`🔧 TENSORS TO DETECTIONS: Created ${detections.length} valid detections`);
    
    return detections;

  } catch (error) {
    console.error('🔧 TENSORS TO DETECTIONS: Error converting tensors:', error);
    console.error('🔧 TENSORS TO DETECTIONS: Tensor shape:', detectionTensor.shape);
    console.error('🔧 TENSORS TO DETECTIONS: Config:', config);
    return [];
  }
}
