
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Detection } from './interfaces/common_interfaces';
import { Anchor } from './create_ssd_anchors';

export interface TensorsToDetectionsConfig {
  numClasses: number;
  numBoxes: number;
  numCoords: number;
  boxCoordOffset: number;
  keypointCoordOffset: number;
  numKeypoints: number;
  numValuesPerKeypoint: number;
  sigmoidScore: boolean;
  scoreClippingThresh: number;
  reverseOutputOrder: boolean;
  xScale: number;
  yScale: number;
  wScale: number;
  hScale: number;
  minScoreThresh: number;
}

/**
 * PHASE 1: Converts raw detection tensors to detection objects.
 */
export async function tensorsToDetections(
    detectionTensor: tf.Tensor,
    anchors: Anchor[],
    config: TensorsToDetectionsConfig): Promise<Detection[]> {
  
  console.log('🔧 TENSORS TO DETECTIONS: Converting tensor to detections');
  console.log('🔧 TENSORS TO DETECTIONS: Tensor shape:', detectionTensor.shape);
  console.log('🔧 TENSORS TO DETECTIONS: Anchors:', anchors.length);
  console.log('🔧 TENSORS TO DETECTIONS: Config numCoords:', config.numCoords);

  const detections: Detection[] = [];
  
  if (!detectionTensor || anchors.length === 0) {
    console.warn('🔧 TENSORS TO DETECTIONS: No tensor or anchors provided');
    return detections;
  }

  // Validate tensor dimensions
  if (detectionTensor.shape.length !== 3) {
    console.error('🔧 TENSORS TO DETECTIONS: Expected 3D tensor, got shape:', detectionTensor.shape);
    return detections;
  }

  const [batchSize, numBoxes, numCoords] = detectionTensor.shape;
  console.log('🔧 TENSORS TO DETECTIONS: Tensor dimensions - batch:', batchSize, 'boxes:', numBoxes, 'coords:', numCoords);

  // TASK 1: BlazePose-specific tensor shape validation
  if (batchSize !== 1) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects batch size 1, got:', batchSize);
    return detections;
  }

  if (numCoords !== 13) {
    console.error('🔧 TENSORS TO DETECTIONS: BlazePose expects 13 coordinates per box, got:', numCoords);
    console.error('🔧 TENSORS TO DETECTIONS: Expected format: [yCenter, xCenter, height, width, score, ...8 additional values]');
    return detections;
  }

  console.log('✅ TENSORS TO DETECTIONS: BlazePose tensor shape validation passed');

  try {
    // TASK 5: Comprehensive tensor debugging starts here
    const startTime = performance.now();
    console.log('🔧 TENSORS TO DETECTIONS: Starting comprehensive tensor data extraction');
    
    // TASK 5: Log detailed tensor metadata
    console.log('🔧 TENSORS TO DETECTIONS: Tensor metadata:', {
      shape: detectionTensor.shape,
      dtype: detectionTensor.dtype,
      size: detectionTensor.size,
      rank: detectionTensor.rank,
      memory: `${(detectionTensor.size * 4 / 1024).toFixed(2)} KB` // Float32 = 4 bytes
    });
    
    // Get tensor data as Float32Array for proper indexing
    const tensorData = await detectionTensor.data();
    const extractionTime = performance.now() - startTime;
    console.log(`🔧 TENSORS TO DETECTIONS: Tensor data extraction completed in ${extractionTime.toFixed(2)}ms`);
    
    // Validate tensor data extraction
    if (!tensorData || tensorData.length === 0) {
      console.error('🔧 TENSORS TO DETECTIONS: Tensor data extraction failed - empty or null data');
      return detections;
    }
    
    const expectedDataLength = batchSize * numBoxes * numCoords;
    if (tensorData.length !== expectedDataLength) {
      console.error(`🔧 TENSORS TO DETECTIONS: Tensor data length mismatch. Expected: ${expectedDataLength}, Got: ${tensorData.length}`);
      return detections;
    }
    
    console.log(`✅ TENSORS TO DETECTIONS: Tensor data extracted successfully - ${tensorData.length} values`);
    
    // TASK 5: Statistical analysis of tensor data
    const tensorDataArray = Array.from(tensorData);
    const tensorStats = {
      min: Math.min(...tensorDataArray),
      max: Math.max(...tensorDataArray),
      mean: tensorDataArray.reduce((sum, val) => sum + val, 0) / tensorDataArray.length,
      nonZeroCount: tensorDataArray.filter(v => v !== 0).length,
      infiniteCount: tensorDataArray.filter(v => !isFinite(v)).length
    };
    
    console.log('🔧 TENSORS TO DETECTIONS: Tensor data statistics:', {
      ...tensorStats,
      mean: tensorStats.mean.toFixed(4),
      nonZeroRatio: `${(tensorStats.nonZeroCount / tensorData.length * 100).toFixed(1)}%`,
      validRatio: `${((tensorData.length - tensorStats.infiniteCount) / tensorData.length * 100).toFixed(1)}%`
    });
    
    // Calculate number of detections to process
    const numDetections = Math.min(numBoxes, anchors.length);
    console.log('🔧 TENSORS TO DETECTIONS: Processing', numDetections, 'detections');
    
    // TASK 5: Log anchor information summary
    const anchorStats = {
      total: anchors.length,
      validAnchors: 0,
      avgWidth: 0,
      avgHeight: 0,
      minWidth: Number.MAX_VALUE,
      maxWidth: Number.MIN_VALUE,
      minHeight: Number.MAX_VALUE,
      maxHeight: Number.MIN_VALUE
    };
    
    let validAnchorSum = { width: 0, height: 0 };
    anchors.slice(0, numDetections).forEach((anchor, i) => {
      if (anchor && typeof anchor.width === 'number' && typeof anchor.height === 'number' && 
          anchor.width > 0 && anchor.height > 0) {
        anchorStats.validAnchors++;
        validAnchorSum.width += anchor.width;
        validAnchorSum.height += anchor.height;
        anchorStats.minWidth = Math.min(anchorStats.minWidth, anchor.width);
        anchorStats.maxWidth = Math.max(anchorStats.maxWidth, anchor.width);
        anchorStats.minHeight = Math.min(anchorStats.minHeight, anchor.height);
        anchorStats.maxHeight = Math.max(anchorStats.maxHeight, anchor.height);
      }
    });
    
    if (anchorStats.validAnchors > 0) {
      anchorStats.avgWidth = validAnchorSum.width / anchorStats.validAnchors;
      anchorStats.avgHeight = validAnchorSum.height / anchorStats.validAnchors;
    }
    
    console.log('🔧 TENSORS TO DETECTIONS: Anchor statistics:', {
      ...anchorStats,
      avgWidth: anchorStats.avgWidth.toFixed(4),
      avgHeight: anchorStats.avgHeight.toFixed(4),
      validRatio: `${(anchorStats.validAnchors / anchors.length * 100).toFixed(1)}%`
    });
    
    // TASK 4: Log sample tensor data for debugging
    if (numDetections > 0) {
      const sampleIndex = 0 * numBoxes * numCoords;
      console.log('🔧 TENSORS TO DETECTIONS: Sample tensor data for first detection:', {
        indices: `[${sampleIndex}:${sampleIndex + 12}]`,
        values: Array.from(tensorData.slice(sampleIndex, sampleIndex + 13)).map(v => v.toFixed(4))
      });
    }

    for (let i = 0; i < numDetections; i++) {
      const anchor = anchors[i];
      
      // TASK 3: Validate anchor data integrity
      if (!anchor || typeof anchor.xCenter !== 'number' || typeof anchor.yCenter !== 'number' ||
          typeof anchor.width !== 'number' || typeof anchor.height !== 'number') {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at index ${i}:`, anchor);
        continue;
      }

      if (anchor.width <= 0 || anchor.height <= 0) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor dimensions at index ${i}: width=${anchor.width}, height=${anchor.height}`);
        continue;
      }
      
      // Correct 3D tensor indexing: batch=0, detection=i, values=0-12
      // Formula: tensorData[batch * numBoxes * numCoords + detection * numCoords + valueIndex]
      const baseIndex = 0 * numBoxes * numCoords + i * numCoords;
      
      // TASK 4: Bounds checking for tensor access
      if (baseIndex < 0 || baseIndex + 12 >= tensorData.length) {
        console.error(`🔧 TENSORS TO DETECTIONS: Tensor access out of bounds at detection ${i}. BaseIndex: ${baseIndex}, TensorLength: ${tensorData.length}`);
        continue;
      }
      
      // Extract box coordinates (first 4 values)
      // BlazePose format: [yCenter, xCenter, height, width, score, ...]
      const yCenter = tensorData[baseIndex + 0];
      const xCenter = tensorData[baseIndex + 1]; 
      const h = tensorData[baseIndex + 2];
      const w = tensorData[baseIndex + 3];

      // TASK 4: Log detailed extraction for first few detections
      if (i < 3) {
        console.log(`🔧 TENSORS TO DETECTIONS: Detection ${i} raw values:`, {
          baseIndex,
          yCenter: yCenter.toFixed(4),
          xCenter: xCenter.toFixed(4),
          height: h.toFixed(4),
          width: w.toFixed(4),
          rawScore: tensorData[baseIndex + 4].toFixed(4)
        });
      }

      // Validate extracted coordinates
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w)) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid coordinates at detection ${i}: yCenter=${yCenter}, xCenter=${xCenter}, h=${h}, w=${w}`);
        continue;
      }
      
      // Extract and process score (5th value, index 4)
      let score = tensorData[baseIndex + 4];
      if (config.sigmoidScore) {
        score = 1.0 / (1.0 + Math.exp(-score));
      }

      // Validate score
      if (!isFinite(score) || score < 0 || score > 1) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid score at detection ${i}: ${score}`);
        continue;
      }

      // Apply score threshold early
      if (score < config.minScoreThresh) {
        continue;
      }

      // TASK 3: FIXED - BlazePose coordinate transformation with proper scaling
      // BlazePose outputs normalized coordinates that need proper scaling
      
      // Validate input values first
      if (!isFinite(yCenter) || !isFinite(xCenter) || !isFinite(h) || !isFinite(w)) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid raw coordinates at detection ${i}:`, {
          yCenter, xCenter, h, w
        });
        continue;
      }
      
      // Validate anchor values
      if (!isFinite(anchor.xCenter) || !isFinite(anchor.yCenter) || 
          !isFinite(anchor.width) || !isFinite(anchor.height) ||
          anchor.width <= 0 || anchor.height <= 0) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Invalid anchor at detection ${i}:`, anchor);
        continue;
      }
      
      // FIXED: Use correct BlazePose coordinate transformation without scaling issues
      // BlazePose coordinates are already normalized relative to the input image
      let anchorCenterX, anchorCenterY, boxWidth, boxHeight;
      
      try {
        // FINAL FIX: Use proper BlazePose coordinate transformation
        console.log(`🔧 TRANSFORM DEBUG ${i}: Raw values`, { yCenter, xCenter, h, w, anchor });
        
        // BlazePose outputs coordinates in a specific format - use minimal scaling
        // The raw values are already close to the expected range
        const scaleX = 0.01; // Very small scale for center offset
        const scaleY = 0.01; // Very small scale for center offset  
        const sizeScaleW = 0.05; // Small scale for width
        const sizeScaleH = 0.05; // Small scale for height
        
        anchorCenterX = anchor.xCenter + (xCenter * scaleX);
        anchorCenterY = anchor.yCenter + (yCenter * scaleY); 
        boxWidth = Math.max(0.001, anchor.width * Math.abs(w) * sizeScaleW);
        boxHeight = Math.max(0.001, anchor.height * Math.abs(h) * sizeScaleH);
        
        console.log(`🔧 TRANSFORM DEBUG ${i}: Transformed`, { anchorCenterX, anchorCenterY, boxWidth, boxHeight });
        
        // Additional safety checks
        if (!isFinite(anchorCenterX)) anchorCenterX = anchor.xCenter;
        if (!isFinite(anchorCenterY)) anchorCenterY = anchor.yCenter;
        if (!isFinite(boxWidth) || boxWidth <= 0) boxWidth = anchor.width * 0.1;
        if (!isFinite(boxHeight) || boxHeight <= 0) boxHeight = anchor.height * 0.1;
        
      } catch (transformError) {
        console.warn(`🔧 TENSORS TO DETECTIONS: Transform error at detection ${i}:`, transformError);
        // Use safe fallback values
        anchorCenterX = anchor.xCenter;
        anchorCenterY = anchor.yCenter;
        boxWidth = anchor.width * 0.1;
        boxHeight = anchor.height * 0.1;
      }

      // EXTREMELY RELAXED validation - allow BlazePose detections for full-frame runner analysis
      // For a 5-foot runner with limbs extending across full video width, we need very permissive bounds
      if (!isFinite(anchorCenterX) || !isFinite(anchorCenterY) ||
          !isFinite(boxWidth) || !isFinite(boxHeight) ||
          boxWidth <= 0 || boxHeight <= 0 ||
          Math.abs(anchorCenterX) > 10.0 || Math.abs(anchorCenterY) > 10.0 ||
          boxWidth > 5.0 || boxHeight > 5.0) {
        console.warn(`🔧 REJECT DETECTION ${i}: Invalid bounds`, {
          anchorCenterX: anchorCenterX?.toFixed(6) || 'NaN',
          anchorCenterY: anchorCenterY?.toFixed(6) || 'NaN',
          boxWidth: boxWidth?.toFixed(6) || 'NaN',
          boxHeight: boxHeight?.toFixed(6) || 'NaN'
        });
        continue;
      }

      // Calculate bounding box corners
      const xMin = anchorCenterX - boxWidth / 2;
      const yMin = anchorCenterY - boxHeight / 2;
      const xMax = anchorCenterX + boxWidth / 2;
      const yMax = anchorCenterY + boxHeight / 2;

      // Ensure coordinates are normalized (0-1 range) and positive
      const normalizedXMin = Math.max(0, Math.min(1, xMin));
      const normalizedYMin = Math.max(0, Math.min(1, yMin));
      const normalizedXMax = Math.max(0, Math.min(1, xMax));
      const normalizedYMax = Math.max(0, Math.min(1, yMax));
      const normalizedWidth = normalizedXMax - normalizedXMin;
      const normalizedHeight = normalizedYMax - normalizedYMin;

      // SAFE detection object creation with validation
      const detection: Detection = {
        boundingBox: {
          xMin: Math.max(0, Math.min(1, normalizedXMin)),
          yMin: Math.max(0, Math.min(1, normalizedYMin)),
          xMax: Math.max(0, Math.min(1, normalizedXMax)),
          yMax: Math.max(0, Math.min(1, normalizedYMax)),
          width: Math.max(0.001, Math.min(1, normalizedWidth)),
          height: Math.max(0.001, Math.min(1, normalizedHeight))
        },
        relativeBoundingBox: {
          xCenter: Math.max(-10, Math.min(10, anchorCenterX)),
          yCenter: Math.max(-10, Math.min(10, anchorCenterY)),
          width: Math.max(0.001, Math.min(5, boxWidth)),
          height: Math.max(0.001, Math.min(5, boxHeight)),
          rotation: 0
        },
        score: Math.max(0, Math.min(1, score))
      };
      
      console.log(`🔧 CREATED DETECTION ${i}:`, {
        score: detection.score.toFixed(3),
        bbox: detection.boundingBox,
        relBbox: detection.relativeBoundingBox
      });

      detections.push(detection);
    }

    // TASK 5: Processing summary and performance metrics
    const totalProcessingTime = performance.now() - startTime;
    const processingStats = {
      totalTime: `${totalProcessingTime.toFixed(2)}ms`,
      extractionTime: `${extractionTime.toFixed(2)}ms`,
      processingTime: `${(totalProcessingTime - extractionTime).toFixed(2)}ms`,
      detectionsProcessed: numDetections,
      validDetections: detections.length,
      successRate: `${(detections.length / numDetections * 100).toFixed(1)}%`,
      avgTimePerDetection: `${((totalProcessingTime - extractionTime) / numDetections).toFixed(3)}ms`
    };
    
    console.log('🔧 TENSORS TO DETECTIONS: Processing summary:', processingStats);
    console.log(`🔧 TENSORS TO DETECTIONS: Created ${detections.length} valid detections from ${numDetections} processed`);
    if (detections.length > 0) {
      console.log('🔧 TENSORS TO DETECTIONS: Sample detection:', {
        score: detections[0].score.toFixed(3),
        boundingBox: detections[0].boundingBox,
        relativeBoundingBox: detections[0].relativeBoundingBox
      });
    }
    
    return detections;

  } catch (error) {
    console.error('🔧 TENSORS TO DETECTIONS: Error converting tensors:', error);
    console.error('🔧 TENSORS TO DETECTIONS: Tensor shape:', detectionTensor.shape);
    console.error('🔧 TENSORS TO DETECTIONS: Config:', config);
    return [];
  }
}
