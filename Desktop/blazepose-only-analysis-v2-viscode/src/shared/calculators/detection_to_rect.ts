/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

import {normalizeRadians} from './image_utils';
import {ImageSize} from './interfaces/common_interfaces';
import {DetectionToRectConfig} from './interfaces/config_interfaces';
import {BoundingBox, Detection, LocationData, Rect} from './interfaces/shape_interfaces';

// ref:
// https://github.com/google/mediapipe/blob/master/mediapipe/calculators/util/detections_to_rects_calculator.cc
export function computeRotation(
    detection: Detection, imageSize: ImageSize, config: DetectionToRectConfig) {
  console.log('🔧 DETECTION_TO_RECT: Computing rotation for detection');
  
  // TASK 2: Add comprehensive null/undefined checks for all parameters
  if (!detection) {
    console.error('🔧 DETECTION_TO_RECT: Detection object is null or undefined');
    return 0; // Default rotation
  }

  if (!imageSize) {
    console.error('🔧 DETECTION_TO_RECT: ImageSize is null or undefined');
    return 0; // Default rotation
  }

  if (!config) {
    console.error('🔧 DETECTION_TO_RECT: DetectionToRectConfig is null or undefined');
    return 0; // Default rotation
  }

  // TASK 2: Validate imageSize properties
  if (typeof imageSize.width !== 'number' || typeof imageSize.height !== 'number' ||
      !isFinite(imageSize.width) || !isFinite(imageSize.height) ||
      imageSize.width <= 0 || imageSize.height <= 0) {
    console.error('🔧 DETECTION_TO_RECT: Invalid imageSize properties:', imageSize);
    return 0; // Default rotation
  }

  if (!detection.locationData) {
    console.error('🔧 DETECTION_TO_RECT: Detection object missing locationData for rotation calculation');
    return 0; // Default rotation
  }

  const locationData = detection.locationData;
  
  // TASK 2: Validate config parameters with comprehensive checks
  if (config.rotationVectorStartKeypointIndex == null || 
      config.rotationVectorEndKeypointIndex == null) {
    console.error('🔧 DETECTION_TO_RECT: Missing keypoint indices in config');
    console.error('🔧 DETECTION_TO_RECT: Start index:', config.rotationVectorStartKeypointIndex, 'End index:', config.rotationVectorEndKeypointIndex);
    return 0; // Default rotation
  }

  // TASK 2: Validate keypoint indices are numbers and non-negative
  const startKeypoint = config.rotationVectorStartKeypointIndex;
  const endKeypoint = config.rotationVectorEndKeypointIndex;

  if (typeof startKeypoint !== 'number' || typeof endKeypoint !== 'number' ||
      !isFinite(startKeypoint) || !isFinite(endKeypoint) ||
      startKeypoint < 0 || endKeypoint < 0) {
    console.error('🔧 DETECTION_TO_RECT: Invalid keypoint indices:', {startKeypoint, endKeypoint});
    return 0; // Default rotation
  }

  // TASK 2: Comprehensive validation for keypoints array
  if (!locationData.relativeKeypoints) {
    console.error('🔧 DETECTION_TO_RECT: relativeKeypoints array is null or undefined');
    return 0; // Default rotation
  }

  if (!Array.isArray(locationData.relativeKeypoints)) {
    console.error('🔧 DETECTION_TO_RECT: relativeKeypoints is not an array:', typeof locationData.relativeKeypoints);
    return 0; // Default rotation
  }
      
  if (locationData.relativeKeypoints.length <= startKeypoint ||
      locationData.relativeKeypoints.length <= endKeypoint) {
    console.error('🔧 DETECTION_TO_RECT: Keypoint indices out of bounds');
    console.error('🔧 DETECTION_TO_RECT: Keypoints length:', locationData.relativeKeypoints.length);
    console.error('🔧 DETECTION_TO_RECT: Start index:', startKeypoint, 'End index:', endKeypoint);
    return 0; // Default rotation
  }

  // TASK 2: Validate individual keypoints before use
  const startKeypointData = locationData.relativeKeypoints[startKeypoint];
  const endKeypointData = locationData.relativeKeypoints[endKeypoint];

  if (!startKeypointData || !endKeypointData) {
    console.error('🔧 DETECTION_TO_RECT: Keypoint data is null or undefined');
    console.error('🔧 DETECTION_TO_RECT: Start keypoint:', startKeypointData, 'End keypoint:', endKeypointData);
    return 0; // Default rotation
  }

  if (typeof startKeypointData.x !== 'number' || typeof startKeypointData.y !== 'number' ||
      typeof endKeypointData.x !== 'number' || typeof endKeypointData.y !== 'number') {
    console.error('🔧 DETECTION_TO_RECT: Keypoint coordinates are not numbers');
    console.error('🔧 DETECTION_TO_RECT: Start keypoint:', startKeypointData, 'End keypoint:', endKeypointData);
    return 0; // Default rotation
  }

  if (!isFinite(startKeypointData.x) || !isFinite(startKeypointData.y) ||
      !isFinite(endKeypointData.x) || !isFinite(endKeypointData.y)) {
    console.error('🔧 DETECTION_TO_RECT: Keypoint coordinates contain non-finite values');
    console.error('🔧 DETECTION_TO_RECT: Start keypoint:', startKeypointData, 'End keypoint:', endKeypointData);
    return 0; // Default rotation
  }

  // TASK 2: Validate target angle configuration
  let targetAngle;
  if (config.rotationVectorTargetAngle != null) {
    if (typeof config.rotationVectorTargetAngle !== 'number' || !isFinite(config.rotationVectorTargetAngle)) {
      console.error('🔧 DETECTION_TO_RECT: Invalid rotationVectorTargetAngle:', config.rotationVectorTargetAngle);
      return 0; // Default rotation
    }
    targetAngle = config.rotationVectorTargetAngle;
  } else if (config.rotationVectorTargetAngleDegree != null) {
    if (typeof config.rotationVectorTargetAngleDegree !== 'number' || !isFinite(config.rotationVectorTargetAngleDegree)) {
      console.error('🔧 DETECTION_TO_RECT: Invalid rotationVectorTargetAngleDegree:', config.rotationVectorTargetAngleDegree);
      return 0; // Default rotation
    }
    targetAngle = Math.PI * config.rotationVectorTargetAngleDegree / 180;
  } else {
    console.error('🔧 DETECTION_TO_RECT: No valid target angle configuration found');
    return 0; // Default rotation
  }

  const x0 = startKeypointData.x * imageSize.width;
  const y0 = startKeypointData.y * imageSize.height;
  const x1 = endKeypointData.x * imageSize.width;
  const y1 = endKeypointData.y * imageSize.height;

  console.log('🔧 DETECTION_TO_RECT: Keypoint coordinates - start:', {x: x0, y: y0}, 'end:', {x: x1, y: y1});

  const rotation = normalizeRadians(targetAngle - Math.atan2(-(y1 - y0), x1 - x0));

  // TASK 2: Validate computed rotation
  if (!isFinite(rotation)) {
    console.error('🔧 DETECTION_TO_RECT: Computed rotation is not finite:', rotation);
    return 0; // Default rotation
  }

  console.log('🔧 DETECTION_TO_RECT: Computed rotation:', rotation);
  return rotation;
}

// TASK 6: Validate rect conversion logic
function validateRectConversion(originalBox: BoundingBox, convertedRect: Rect, isNormalized: boolean = true): boolean {
  console.log('✅ DETECTION_TO_RECT: Validating rect conversion');
  console.log('✅ DETECTION_TO_RECT: Original box:', originalBox);
  console.log('✅ DETECTION_TO_RECT: Converted rect:', convertedRect);
  
  let isValid = true;
  const errors = [];

  // Validate rect properties exist and are numbers
  const requiredProps = ['xCenter', 'yCenter', 'width', 'height'];
  for (const prop of requiredProps) {
    if (typeof convertedRect[prop as keyof Rect] !== 'number' || 
        !isFinite(convertedRect[prop as keyof Rect] as number)) {
      errors.push(`Invalid ${prop}: ${convertedRect[prop as keyof Rect]}`);
      isValid = false;
    }
  }

  // Validate mathematical consistency between bounding box and rect
  const expectedXCenter = originalBox.xMin + originalBox.width / 2;
  const expectedYCenter = originalBox.yMin + originalBox.height / 2;
  
  const centerErrorX = Math.abs(convertedRect.xCenter - expectedXCenter);
  const centerErrorY = Math.abs(convertedRect.yCenter - expectedYCenter);
  const widthError = Math.abs(convertedRect.width - originalBox.width);
  const heightError = Math.abs(convertedRect.height - originalBox.height);
  
  const tolerance = isNormalized ? 0.001 : 1.0; // Allow small floating point errors
  
  if (centerErrorX > tolerance) {
    errors.push(`X center mismatch: expected ${expectedXCenter}, got ${convertedRect.xCenter}, error: ${centerErrorX}`);
    isValid = false;
  }
  
  if (centerErrorY > tolerance) {
    errors.push(`Y center mismatch: expected ${expectedYCenter}, got ${convertedRect.yCenter}, error: ${centerErrorY}`);
    isValid = false;
  }
  
  if (widthError > tolerance) {
    errors.push(`Width mismatch: expected ${originalBox.width}, got ${convertedRect.width}, error: ${widthError}`);
    isValid = false;
  }
  
  if (heightError > tolerance) {
    errors.push(`Height mismatch: expected ${originalBox.height}, got ${convertedRect.height}, error: ${heightError}`);
    isValid = false;
  }

  // Validate coordinate ranges
  if (isNormalized) {
    if (convertedRect.xCenter < 0 || convertedRect.xCenter > 1) {
      errors.push(`Normalized X center out of range: ${convertedRect.xCenter}`);
      isValid = false;
    }
    if (convertedRect.yCenter < 0 || convertedRect.yCenter > 1) {
      errors.push(`Normalized Y center out of range: ${convertedRect.yCenter}`);
      isValid = false;
    }
    if (convertedRect.width < 0 || convertedRect.width > 1) {
      errors.push(`Normalized width out of range: ${convertedRect.width}`);
      isValid = false;
    }
    if (convertedRect.height < 0 || convertedRect.height > 1) {
      errors.push(`Normalized height out of range: ${convertedRect.height}`);
      isValid = false;
    }
  } else {
    if (convertedRect.width <= 0 || convertedRect.height <= 0) {
      errors.push(`Invalid pixel dimensions: width=${convertedRect.width}, height=${convertedRect.height}`);
      isValid = false;
    }
  }

  // Validate rect bounds consistency
  const rectXMin = convertedRect.xCenter - convertedRect.width / 2;
  const rectYMin = convertedRect.yCenter - convertedRect.height / 2;
  const rectXMax = convertedRect.xCenter + convertedRect.width / 2;
  const rectYMax = convertedRect.yCenter + convertedRect.height / 2;
  
  const boundsErrorXMin = Math.abs(rectXMin - originalBox.xMin);
  const boundsErrorYMin = Math.abs(rectYMin - originalBox.yMin);
  const boundsErrorXMax = Math.abs(rectXMax - originalBox.xMax);
  const boundsErrorYMax = Math.abs(rectYMax - originalBox.yMax);
  
  if (boundsErrorXMin > tolerance || boundsErrorYMin > tolerance || 
      boundsErrorXMax > tolerance || boundsErrorYMax > tolerance) {
    errors.push(`Bounds consistency error - computed bounds don't match original`);
    console.log('✅ DETECTION_TO_RECT: Bounds validation details:', {
      original: { xMin: originalBox.xMin, yMin: originalBox.yMin, xMax: originalBox.xMax, yMax: originalBox.yMax },
      computed: { xMin: rectXMin, yMin: rectYMin, xMax: rectXMax, yMax: rectYMax },
      errors: { xMin: boundsErrorXMin, yMin: boundsErrorYMin, xMax: boundsErrorXMax, yMax: boundsErrorYMax }
    });
    isValid = false;
  }

  if (!isValid) {
    console.error('✅ DETECTION_TO_RECT: Rect conversion validation failed:', errors);
  } else {
    console.log('✅ DETECTION_TO_RECT: Rect conversion validation passed');
  }
  
  return isValid;
}

function validateRectEdgeCases(): void {
  console.log('✅ DETECTION_TO_RECT: Testing edge cases for rect conversion');
  
  const edgeCases = [
    // Minimum size box
    { xMin: 0.5, yMin: 0.5, xMax: 0.5001, yMax: 0.5001, width: 0.0001, height: 0.0001 },
    // Zero-width box (degenerate case)
    { xMin: 0.5, yMin: 0.4, xMax: 0.5, yMax: 0.6, width: 0, height: 0.2 },
    // Zero-height box (degenerate case)  
    { xMin: 0.4, yMin: 0.5, xMax: 0.6, yMax: 0.5, width: 0.2, height: 0 },
    // Maximum size box
    { xMin: 0, yMin: 0, xMax: 1, yMax: 1, width: 1, height: 1 },
    // Box extending beyond bounds
    { xMin: -0.1, yMin: -0.1, xMax: 1.1, yMax: 1.1, width: 1.2, height: 1.2 },
    // Very small box near edge
    { xMin: 0.999, yMin: 0.999, xMax: 1.001, yMax: 1.001, width: 0.002, height: 0.002 },
    // Negative coordinates
    { xMin: -0.5, yMin: -0.5, xMax: 0.5, yMax: 0.5, width: 1.0, height: 1.0 }
  ];

  let passedTests = 0;
  let totalTests = edgeCases.length;

  edgeCases.forEach((testCase, index) => {
    console.log(`✅ DETECTION_TO_RECT: Testing edge case ${index + 1}:`, testCase);
    
    try {
      const rect = rectFromBox(testCase, true);
      console.log(`✅ DETECTION_TO_RECT: Edge case ${index + 1} result:`, rect);
      
      // Basic validation - should not throw and should have valid properties
      if (typeof rect.xCenter === 'number' && isFinite(rect.xCenter) &&
          typeof rect.yCenter === 'number' && isFinite(rect.yCenter) &&
          typeof rect.width === 'number' && isFinite(rect.width) && rect.width >= 0 &&
          typeof rect.height === 'number' && isFinite(rect.height) && rect.height >= 0) {
        
        // Additional validation for normalized coordinates
        if (rect.xCenter >= 0 && rect.xCenter <= 1 &&
            rect.yCenter >= 0 && rect.yCenter <= 1 &&
            rect.width >= 0 && rect.width <= 1 &&
            rect.height >= 0 && rect.height <= 1) {
          console.log(`✅ DETECTION_TO_RECT: Edge case ${index + 1} passed`);
          passedTests++;
        } else {
          console.warn(`✅ DETECTION_TO_RECT: Edge case ${index + 1} coordinates out of normalized range`);
        }
      } else {
        console.error(`✅ DETECTION_TO_RECT: Edge case ${index + 1} produced invalid rect properties`);
      }
      
    } catch (error) {
      console.error(`✅ DETECTION_TO_RECT: Edge case ${index + 1} threw error:`, error);
    }
  });
  
  console.log(`✅ DETECTION_TO_RECT: Edge case testing complete: ${passedTests}/${totalTests} tests passed`);
}

function validateConversionPerformance(detection: Detection, iterations: number = 100): void {
  console.log(`✅ DETECTION_TO_RECT: Testing conversion performance over ${iterations} iterations`);
  
  const startTime = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    try {
      detectionToNormalizedRect(detection, 'boundingbox');
    } catch (error) {
      console.error(`✅ DETECTION_TO_RECT: Performance test iteration ${i} failed:`, error);
    }
  }
  
  const endTime = performance.now();
  const totalTime = endTime - startTime;
  const averageTime = totalTime / iterations;
  
  console.log(`✅ DETECTION_TO_RECT: Performance results:`, {
    totalTime: `${totalTime.toFixed(3)}ms`,
    averageTime: `${averageTime.toFixed(3)}ms`,
    iterationsPerSecond: Math.round(1000 / averageTime)
  });
  
  // Flag if performance is unusually slow
  if (averageTime > 1.0) {
    console.warn('✅ DETECTION_TO_RECT: Conversion performance is slow - consider optimization');
  }
}

function rectFromBox(box: BoundingBox, isNormalized: boolean = true) {
  console.log('🔧 DETECTION_TO_RECT: Converting bounding box to rect:', box, 'normalized:', isNormalized);
  
  // TASK 2: Add comprehensive null/undefined checks for bounding box properties
  if (!box) {
    console.error('🔧 DETECTION_TO_RECT: Bounding box is null or undefined');
    return {
      xCenter: isNormalized ? 0.5 : 100,
      yCenter: isNormalized ? 0.5 : 100,
      width: isNormalized ? 0.1 : 20,
      height: isNormalized ? 0.1 : 20,
    };
  }

  // TASK 2: Validate all required bounding box properties
  const requiredProps = ['xMin', 'yMin', 'width', 'height'];
  const missingProps = requiredProps.filter(prop => 
    box[prop as keyof BoundingBox] == null || typeof box[prop as keyof BoundingBox] !== 'number'
  );

  if (missingProps.length > 0) {
    console.error('🔧 DETECTION_TO_RECT: Invalid bounding box - missing or invalid properties:', missingProps);
    console.error('🔧 DETECTION_TO_RECT: Box structure:', box);
    return {
      xCenter: isNormalized ? 0.5 : 100,
      yCenter: isNormalized ? 0.5 : 100,
      width: isNormalized ? 0.1 : 20,
      height: isNormalized ? 0.1 : 20,
    };
  }

  // TASK 2: Additional validation for finite values
  if (!isFinite(box.xMin) || !isFinite(box.yMin) || !isFinite(box.width) || !isFinite(box.height)) {
    console.error('🔧 DETECTION_TO_RECT: Bounding box contains non-finite values:', box);
    return {
      xCenter: isNormalized ? 0.5 : 100,
      yCenter: isNormalized ? 0.5 : 100,
      width: isNormalized ? 0.1 : 20,
      height: isNormalized ? 0.1 : 20,
    };
  }

  // TASK 2: Validate positive dimensions
  if (box.width <= 0 || box.height <= 0) {
    console.warn('🔧 DETECTION_TO_RECT: Bounding box has non-positive dimensions:', {width: box.width, height: box.height});
    return {
      xCenter: isNormalized ? Math.max(0, Math.min(1, box.xMin + Math.abs(box.width) / 2)) : Math.max(0, box.xMin + Math.abs(box.width) / 2),
      yCenter: isNormalized ? Math.max(0, Math.min(1, box.yMin + Math.abs(box.height) / 2)) : Math.max(0, box.yMin + Math.abs(box.height) / 2),
      width: Math.abs(box.width) || (isNormalized ? 0.1 : 20),
      height: Math.abs(box.height) || (isNormalized ? 0.1 : 20),
    };
  }

  let rect = {
    xCenter: box.xMin + box.width / 2,
    yCenter: box.yMin + box.height / 2,
    width: box.width,
    height: box.height,
  };

  // TASK 3: Coordinate normalization validation and clamping
  if (isNormalized) {
    // Ensure normalized coordinates stay in 0-1 range
    if (rect.xCenter < 0 || rect.xCenter > 1 || rect.yCenter < 0 || rect.yCenter > 1) {
      console.warn('🔧 DETECTION_TO_RECT: Normalized coordinates out of bounds, clamping:', rect);
    }
    rect.xCenter = Math.max(0, Math.min(1, rect.xCenter));
    rect.yCenter = Math.max(0, Math.min(1, rect.yCenter));
    rect.width = Math.max(0, Math.min(1, rect.width));
    rect.height = Math.max(0, Math.min(1, rect.height));
    
    // Ensure the rect doesn't exceed image bounds
    if (rect.xCenter + rect.width/2 > 1) {
      rect.xCenter = 1 - rect.width/2;
    }
    if (rect.yCenter + rect.height/2 > 1) {
      rect.yCenter = 1 - rect.height/2;
    }
    if (rect.xCenter - rect.width/2 < 0) {
      rect.xCenter = rect.width/2;
    }
    if (rect.yCenter - rect.height/2 < 0) {
      rect.yCenter = rect.height/2;
    }
  } else {
    // For pixel coordinates, ensure they're positive
    rect.xCenter = Math.max(0, rect.xCenter);
    rect.yCenter = Math.max(0, rect.yCenter);
    rect.width = Math.max(1, rect.width);
    rect.height = Math.max(1, rect.height);
  }

  console.log('🔧 DETECTION_TO_RECT: Converted rect:', rect);
  
  // TASK 6: Validate rect conversion logic
  if (box && isValidBoundingBox(box)) {
    const isValidConversion = validateRectConversion(box, rect, isNormalized);
    if (!isValidConversion) {
      console.error('🔧 DETECTION_TO_RECT: Rect conversion validation failed, but proceeding with result');
    }
  }
  
  return rect;
}

function normRectFromKeypoints(locationData: LocationData) {
  console.log('🔧 DETECTION_TO_RECT: Computing rect from keypoints');
  
  // TASK 2: Add comprehensive null/undefined checks for LocationData
  if (!locationData) {
    console.error('🔧 DETECTION_TO_RECT: LocationData is null or undefined');
    throw new Error('LocationData is required for keypoints conversion');
  }

  // TASK 2: Validate relativeKeypoints array
  if (!locationData.relativeKeypoints) {
    console.error('🔧 DETECTION_TO_RECT: relativeKeypoints array is null or undefined');
    throw new Error('relativeKeypoints array is required for keypoints conversion');
  }

  if (!Array.isArray(locationData.relativeKeypoints)) {
    console.error('🔧 DETECTION_TO_RECT: relativeKeypoints is not an array:', typeof locationData.relativeKeypoints);
    throw new Error('relativeKeypoints must be an array');
  }

  const keypoints = locationData.relativeKeypoints;
  if (keypoints.length <= 1) {
    console.error('🔧 DETECTION_TO_RECT: Insufficient keypoints for rect calculation:', keypoints.length);
    throw new Error('2 or more keypoints required to calculate a rect.');
  }

  // TASK 2: Validate individual keypoints
  const validKeypoints = keypoints.filter((keypoint, index) => {
    if (!keypoint) {
      console.warn(`🔧 DETECTION_TO_RECT: Keypoint ${index} is null or undefined`);
      return false;
    }
    if (typeof keypoint.x !== 'number' || typeof keypoint.y !== 'number') {
      console.warn(`🔧 DETECTION_TO_RECT: Keypoint ${index} has invalid coordinates:`, keypoint);
      return false;
    }
    if (!isFinite(keypoint.x) || !isFinite(keypoint.y)) {
      console.warn(`🔧 DETECTION_TO_RECT: Keypoint ${index} has non-finite coordinates:`, keypoint);
      return false;
    }
    return true;
  });

  if (validKeypoints.length <= 1) {
    console.error('🔧 DETECTION_TO_RECT: Insufficient valid keypoints after validation:', validKeypoints.length);
    throw new Error('2 or more valid keypoints required to calculate a rect.');
  }

  console.log(`🔧 DETECTION_TO_RECT: Using ${validKeypoints.length} valid keypoints out of ${keypoints.length} total`);

  let xMin = Number.MAX_VALUE, yMin = Number.MAX_VALUE, xMax = Number.MIN_VALUE,
      yMax = Number.MIN_VALUE;

  validKeypoints.forEach(keypoint => {
    xMin = Math.min(xMin, keypoint.x);
    xMax = Math.max(xMax, keypoint.x);
    yMin = Math.min(yMin, keypoint.y);
    yMax = Math.max(yMax, keypoint.y);
  });

  const rect = {
    xCenter: (xMin + xMax) / 2,
    yCenter: (yMin + yMax) / 2,
    width: xMax - xMin,
    height: yMax - yMin
  };

  console.log('🔧 DETECTION_TO_RECT: Computed rect from keypoints:', rect);
  return rect;
}

// TASK 5: Add comprehensive fallback mechanisms for missing properties
function getValidBoundingBox(detection: Detection, imageSize?: ImageSize): BoundingBox | null {
  console.log('🔄 DETECTION_TO_RECT: Attempting to extract valid bounding box with fallbacks');
  
  if (!detection) {
    console.error('🔄 DETECTION_TO_RECT: No detection object - cannot extract bounding box');
    return null;
  }

  // Fallback chain for bounding box extraction
  const candidateBoxes = [];

  // Priority 1: Top-level relativeBoundingBox
  if ((detection as any).relativeBoundingBox) {
    candidateBoxes.push({
      source: 'top-level relativeBoundingBox',
      box: (detection as any).relativeBoundingBox,
      isNormalized: true
    });
  }

  // Priority 2: Top-level boundingBox
  if (detection.boundingBox) {
    candidateBoxes.push({
      source: 'top-level boundingBox',
      box: detection.boundingBox,
      isNormalized: true // Assume normalized from tensors_to_detections
    });
  }

  // Priority 3: LocationData relativeBoundingBox
  if (detection.locationData?.relativeBoundingBox) {
    candidateBoxes.push({
      source: 'locationData relativeBoundingBox',
      box: detection.locationData.relativeBoundingBox,
      isNormalized: true
    });
  }

  // Priority 4: LocationData boundingBox
  if (detection.locationData?.boundingBox) {
    candidateBoxes.push({
      source: 'locationData boundingBox',
      box: detection.locationData.boundingBox,
      isNormalized: false // Assume pixel coordinates
    });
  }

  // Priority 5: Generate from keypoints if available
  if (detection.locationData?.relativeKeypoints && Array.isArray(detection.locationData.relativeKeypoints)) {
    try {
      const keypointBox = generateBoundingBoxFromKeypoints(detection.locationData.relativeKeypoints);
      if (keypointBox) {
        candidateBoxes.push({
          source: 'generated from keypoints',
          box: keypointBox,
          isNormalized: true
        });
      }
    } catch (error) {
      console.warn('🔄 DETECTION_TO_RECT: Failed to generate bounding box from keypoints:', error);
    }
  }

  // Priority 6: Generate from top-level keypoints if available
  if (detection.keypoints && Array.isArray(detection.keypoints)) {
    try {
      const keypointBox = generateBoundingBoxFromKeypoints(detection.keypoints, imageSize);
      if (keypointBox) {
        candidateBoxes.push({
          source: 'generated from top-level keypoints',
          box: keypointBox,
          isNormalized: !imageSize // Normalized if no imageSize provided
        });
      }
    } catch (error) {
      console.warn('🔄 DETECTION_TO_RECT: Failed to generate bounding box from top-level keypoints:', error);
    }
  }

  console.log(`🔄 DETECTION_TO_RECT: Found ${candidateBoxes.length} candidate bounding boxes:`, 
    candidateBoxes.map(c => c.source));

  // Find the first valid bounding box
  for (const candidate of candidateBoxes) {
    if (isValidBoundingBox(candidate.box)) {
      console.log('🔄 DETECTION_TO_RECT: Using bounding box from:', candidate.source);
      return candidate.box;
    } else {
      console.warn('🔄 DETECTION_TO_RECT: Invalid bounding box from:', candidate.source, candidate.box);
    }
  }

  // Last resort: Create default bounding box
  console.warn('🔄 DETECTION_TO_RECT: No valid bounding box found, creating default');
  return createDefaultBoundingBox(imageSize);
}

function generateBoundingBoxFromKeypoints(keypoints: Array<{x: number; y: number}>, imageSize?: ImageSize): BoundingBox | null {
  if (!keypoints || keypoints.length === 0) return null;

  const validKeypoints = keypoints.filter(kp => 
    kp && typeof kp.x === 'number' && typeof kp.y === 'number' &&
    isFinite(kp.x) && isFinite(kp.y)
  );

  if (validKeypoints.length === 0) return null;

  let xMin = Number.MAX_VALUE, yMin = Number.MAX_VALUE;
  let xMax = Number.MIN_VALUE, yMax = Number.MIN_VALUE;

  for (const kp of validKeypoints) {
    xMin = Math.min(xMin, kp.x);
    xMax = Math.max(xMax, kp.x);
    yMin = Math.min(yMin, kp.y);
    yMax = Math.max(yMax, kp.y);
  }

  // Add padding around keypoints (10% of size or minimum padding)
  const width = xMax - xMin;
  const height = yMax - yMin;
  const paddingX = Math.max(width * 0.1, imageSize ? 10 / imageSize.width : 0.05);
  const paddingY = Math.max(height * 0.1, imageSize ? 10 / imageSize.height : 0.05);

  return {
    xMin: Math.max(0, xMin - paddingX),
    yMin: Math.max(0, yMin - paddingY),
    xMax: Math.min(imageSize ? imageSize.width : 1, xMax + paddingX),
    yMax: Math.min(imageSize ? imageSize.height : 1, yMax + paddingY),
    width: (xMax - xMin) + 2 * paddingX,
    height: (yMax - yMin) + 2 * paddingY
  };
}

function isValidBoundingBox(box: any): boolean {
  if (!box || typeof box !== 'object') return false;
  
  const requiredProps = ['xMin', 'yMin', 'width', 'height'];
  for (const prop of requiredProps) {
    if (typeof box[prop] !== 'number' || !isFinite(box[prop])) {
      return false;
    }
  }
  
  return box.width > 0 && box.height > 0;
}

function createDefaultBoundingBox(imageSize?: ImageSize): BoundingBox {
  if (imageSize) {
    // Create centered box in pixel coordinates
    const centerX = imageSize.width / 2;
    const centerY = imageSize.height / 2;
    const defaultWidth = Math.min(imageSize.width * 0.1, 50);
    const defaultHeight = Math.min(imageSize.height * 0.1, 50);
    
    return {
      xMin: centerX - defaultWidth / 2,
      yMin: centerY - defaultHeight / 2,
      xMax: centerX + defaultWidth / 2,
      yMax: centerY + defaultHeight / 2,
      width: defaultWidth,
      height: defaultHeight
    };
  } else {
    // Create normalized coordinates
    return {
      xMin: 0.45,
      yMin: 0.45,
      xMax: 0.55,
      yMax: 0.55,
      width: 0.1,
      height: 0.1
    };
  }
}

function detectionToNormalizedRect(
    detection: Detection, conversionMode: 'boundingbox'|'keypoints') {
  console.log('🔧 DETECTION_TO_RECT: Converting detection to normalized rect, mode:', conversionMode);
  
  // TASK 1: Fix relativeBoundingBox/boundingBox property access logic
  if (!detection) {
    console.error('🔧 DETECTION_TO_RECT: No detection object provided');
    return {
      xCenter: 0.5,
      yCenter: 0.5,
      width: 0.1,
      height: 0.1,
    };
  }

  console.log('🔧 DETECTION_TO_RECT: Detection structure validation:', {
    hasTopLevelBoundingBox: !!detection.boundingBox,
    hasRelativeBoundingBox: !!(detection as any).relativeBoundingBox,
    hasLocationData: !!detection.locationData,
    hasLocationDataBoundingBox: !!detection.locationData?.boundingBox,
    hasLocationDataRelativeBoundingBox: !!detection.locationData?.relativeBoundingBox
  });
  
  if (conversionMode === 'boundingbox') {
    // TASK 1: Fix property access - check top-level properties first, then locationData
    
    // Priority 1: Top-level relativeBoundingBox (from tensors_to_detections output)
    if ((detection as any).relativeBoundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using top-level relativeBoundingBox:', (detection as any).relativeBoundingBox);
      return rectFromBox((detection as any).relativeBoundingBox, true);
    }
    
    // Priority 2: Top-level boundingBox (from tensors_to_detections output, already normalized)  
    else if (detection.boundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using top-level boundingBox:', detection.boundingBox);
      return rectFromBox(detection.boundingBox, true);
    }
    
    // Priority 3: LocationData relativeBoundingBox (legacy format)
    else if (detection.locationData?.relativeBoundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using locationData relativeBoundingBox:', detection.locationData.relativeBoundingBox);
      return rectFromBox(detection.locationData.relativeBoundingBox, true);
    } 
    
    // Priority 4: LocationData boundingBox (legacy format, assume needs normalization)
    else if (detection.locationData?.boundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using locationData boundingBox (normalizing):', detection.locationData.boundingBox);
      const normalizedBox = {
        xMin: Math.max(0, Math.min(1, detection.locationData.boundingBox.xMin)),
        yMin: Math.max(0, Math.min(1, detection.locationData.boundingBox.yMin)),
        width: Math.max(0, Math.min(1, detection.locationData.boundingBox.width)),
        height: Math.max(0, Math.min(1, detection.locationData.boundingBox.height)),
        xMax: Math.max(0, Math.min(1, detection.locationData.boundingBox.xMax)),
        yMax: Math.max(0, Math.min(1, detection.locationData.boundingBox.yMax))
      };
      return rectFromBox(normalizedBox, true);
    }
    
    // TASK 5: Use comprehensive fallback mechanism
    const validBox = getValidBoundingBox(detection);
    if (validBox) {
      console.log('🔄 DETECTION_TO_RECT: Using fallback bounding box');
      return rectFromBox(validBox, true);
    }
    
    // Final fallback if even the comprehensive mechanism fails
    console.error('🔄 DETECTION_TO_RECT: All fallback mechanisms failed - using default rect');
    return {
      xCenter: 0.5,
      yCenter: 0.5,
      width: 0.1,
      height: 0.1,
    };
  } else {
    // Keypoints mode - use locationData
    if (!detection.locationData) {
      console.error('🔧 DETECTION_TO_RECT: Keypoints mode requires locationData');
      return {
        xCenter: 0.5,
        yCenter: 0.5,
        width: 0.1,
        height: 0.1,
      };
    }
    return normRectFromKeypoints(detection.locationData);
  }
}

function detectionToRect(
    detection: Detection,
    conversionMode: 'boundingbox'|'keypoints',
    imageSize?: ImageSize,
    ): Rect {
  console.log('🔧 DETECTION_TO_RECT: Converting detection to pixel rect, mode:', conversionMode);
  
  // TASK 1: Fix relativeBoundingBox/boundingBox property access logic
  if (!detection) {
    console.error('🔧 DETECTION_TO_RECT: No detection object provided');
    return {
      xCenter: imageSize?.width ? imageSize.width / 2 : 100,
      yCenter: imageSize?.height ? imageSize.height / 2 : 100,
      width: imageSize?.width ? imageSize.width * 0.1 : 20,
      height: imageSize?.height ? imageSize.height * 0.1 : 20,
    };
  }

  console.log('🔧 DETECTION_TO_RECT: Detection structure validation for pixel conversion:', {
    hasTopLevelBoundingBox: !!detection.boundingBox,
    hasRelativeBoundingBox: !!(detection as any).relativeBoundingBox,
    hasLocationData: !!detection.locationData,
    hasLocationDataBoundingBox: !!detection.locationData?.boundingBox,
    hasLocationDataRelativeBoundingBox: !!detection.locationData?.relativeBoundingBox,
    imageSize: imageSize
  });

  let rect: Rect;
  if (conversionMode === 'boundingbox') {
    // TASK 1: Fix property access - check top-level properties first, then locationData
    
    // Priority 1: Top-level boundingBox (from tensors_to_detections, normalized coordinates)
    if (detection.boundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using top-level boundingBox for pixel conversion:', detection.boundingBox);
      if (imageSize) {
        // Convert normalized coordinates to pixel coordinates
        const pixelBox = {
          xMin: detection.boundingBox.xMin * imageSize.width,
          yMin: detection.boundingBox.yMin * imageSize.height,
          width: detection.boundingBox.width * imageSize.width,
          height: detection.boundingBox.height * imageSize.height,
          xMax: detection.boundingBox.xMax * imageSize.width,
          yMax: detection.boundingBox.yMax * imageSize.height
        };
        rect = rectFromBox(pixelBox, false);
      } else {
        console.warn('🔧 DETECTION_TO_RECT: No imageSize provided, returning normalized coordinates');
        rect = rectFromBox(detection.boundingBox, true);
      }
    }
    
    // Priority 2: Top-level relativeBoundingBox (from tensors_to_detections, normalized coordinates)
    else if ((detection as any).relativeBoundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using top-level relativeBoundingBox for pixel conversion:', (detection as any).relativeBoundingBox);
      if (imageSize) {
        const pixelBox = {
          xMin: (detection as any).relativeBoundingBox.xMin * imageSize.width,
          yMin: (detection as any).relativeBoundingBox.yMin * imageSize.height,
          width: (detection as any).relativeBoundingBox.width * imageSize.width,
          height: (detection as any).relativeBoundingBox.height * imageSize.height,
          xMax: (detection as any).relativeBoundingBox.xMax * imageSize.width,
          yMax: (detection as any).relativeBoundingBox.yMax * imageSize.height
        };
        rect = rectFromBox(pixelBox, false);
      } else {
        console.warn('🔧 DETECTION_TO_RECT: No imageSize provided, returning normalized coordinates');
        rect = rectFromBox((detection as any).relativeBoundingBox, true);
      }
    }
    
    // Priority 3: LocationData boundingBox (legacy format, assume pixel coordinates)
    else if (detection.locationData?.boundingBox) {
      console.log('🔧 DETECTION_TO_RECT: Using locationData boundingBox for pixel coordinates:', detection.locationData.boundingBox);
      rect = rectFromBox(detection.locationData.boundingBox, false);
    } 
    
    // Priority 4: LocationData relativeBoundingBox (legacy format, needs conversion)
    else if (detection.locationData?.relativeBoundingBox && imageSize) {
      console.log('🔧 DETECTION_TO_RECT: Converting locationData relativeBoundingBox to pixel coordinates');
      const pixelBox = {
        xMin: detection.locationData.relativeBoundingBox.xMin * imageSize.width,
        yMin: detection.locationData.relativeBoundingBox.yMin * imageSize.height,
        width: detection.locationData.relativeBoundingBox.width * imageSize.width,
        height: detection.locationData.relativeBoundingBox.height * imageSize.height,
        xMax: detection.locationData.relativeBoundingBox.xMax * imageSize.width,
        yMax: detection.locationData.relativeBoundingBox.yMax * imageSize.height
      };
      rect = rectFromBox(pixelBox, false);
    }
    
    // TASK 5: Use comprehensive fallback mechanism
    const validBox = getValidBoundingBox(detection, imageSize);
    if (validBox) {
      console.log('🔄 DETECTION_TO_RECT: Using fallback bounding box for pixel conversion');
      if (imageSize) {
        // Convert to pixel coordinates if needed
        const pixelBox = {
          xMin: validBox.xMin * imageSize.width,
          yMin: validBox.yMin * imageSize.height,
          width: validBox.width * imageSize.width,
          height: validBox.height * imageSize.height,
          xMax: validBox.xMax * imageSize.width,
          yMax: validBox.yMax * imageSize.height
        };
        rect = rectFromBox(pixelBox, false);
      } else {
        rect = rectFromBox(validBox, true);
      }
    }
    // Final fallback
    else {
      console.error('🔄 DETECTION_TO_RECT: All fallback mechanisms failed for pixel conversion');
      rect = {
        xCenter: imageSize?.width ? imageSize.width / 2 : 100,
        yCenter: imageSize?.height ? imageSize.height / 2 : 100,
        width: imageSize?.width ? imageSize.width * 0.1 : 20,
        height: imageSize?.height ? imageSize.height * 0.1 : 20,
      };
    }
  } else {
    // Keypoints mode - requires locationData
    if (!detection.locationData) {
      console.error('🔧 DETECTION_TO_RECT: Keypoints mode requires locationData, but none found');
      return {
        xCenter: imageSize?.width ? imageSize.width / 2 : 100,
        yCenter: imageSize?.height ? imageSize.height / 2 : 100,
        width: imageSize?.width ? imageSize.width * 0.1 : 20,
        height: imageSize?.height ? imageSize.height * 0.1 : 20,
      };
    }
    
    rect = normRectFromKeypoints(detection.locationData);
    
    // Convert normalized keypoint rect to pixel coordinates
    if (!imageSize || typeof imageSize.width !== 'number' || typeof imageSize.height !== 'number') {
      console.error('🔧 DETECTION_TO_RECT: Invalid imageSize for keypoints conversion:', imageSize);
      return rect; // Return normalized coordinates as fallback
    }

    const {width, height} = imageSize;
    rect.xCenter = Math.round(rect.xCenter * width);
    rect.yCenter = Math.round(rect.yCenter * height);
    rect.width = Math.round(rect.width * width);
    rect.height = Math.round(rect.height * height);
  }

  console.log('🔧 DETECTION_TO_RECT: Final converted pixel rect:', rect);
  return rect;
}

// ref:
// https://github.com/google/mediapipe/blob/master/mediapipe/calculators/util/detections_to_rects_calculator.cc
// TASK 4: Test both normalized and absolute coordinate paths
function testCoordinateConversions(detection: Detection, imageSize?: ImageSize) {
  console.log('🧪 DETECTION_TO_RECT: Testing coordinate conversion paths');
  
  if (!detection || !imageSize) {
    console.log('🧪 DETECTION_TO_RECT: Skipping coordinate tests - missing detection or imageSize');
    return;
  }

  try {
    // Test normalized path
    const normalizedRect = detectionToNormalizedRect(detection, 'boundingbox');
    console.log('🧪 DETECTION_TO_RECT: Normalized rect result:', normalizedRect);
    
    // Validate normalized coordinates are in 0-1 range
    const isNormalizedValid = 
      normalizedRect.xCenter >= 0 && normalizedRect.xCenter <= 1 &&
      normalizedRect.yCenter >= 0 && normalizedRect.yCenter <= 1 &&
      normalizedRect.width >= 0 && normalizedRect.width <= 1 &&
      normalizedRect.height >= 0 && normalizedRect.height <= 1;
    
    console.log('🧪 DETECTION_TO_RECT: Normalized coordinates valid:', isNormalizedValid);
    if (!isNormalizedValid) {
      console.error('🧪 DETECTION_TO_RECT: Invalid normalized coordinates detected:', normalizedRect);
    }

    // Test pixel path
    const pixelRect = detectionToRect(detection, 'boundingbox', imageSize);
    console.log('🧪 DETECTION_TO_RECT: Pixel rect result:', pixelRect);
    
    // Validate pixel coordinates are positive and reasonable
    const isPixelValid = 
      pixelRect.xCenter >= 0 && pixelRect.xCenter <= imageSize.width &&
      pixelRect.yCenter >= 0 && pixelRect.yCenter <= imageSize.height &&
      pixelRect.width > 0 && pixelRect.width <= imageSize.width &&
      pixelRect.height > 0 && pixelRect.height <= imageSize.height;
    
    console.log('🧪 DETECTION_TO_RECT: Pixel coordinates valid:', isPixelValid);
    if (!isPixelValid) {
      console.error('🧪 DETECTION_TO_RECT: Invalid pixel coordinates detected:', pixelRect);
    }

    // Test conversion consistency: normalized → pixel → normalized
    const reconvertedNormalized = {
      xCenter: pixelRect.xCenter / imageSize.width,
      yCenter: pixelRect.yCenter / imageSize.height,
      width: pixelRect.width / imageSize.width,
      height: pixelRect.height / imageSize.height
    };
    
    const conversionError = {
      xCenter: Math.abs(normalizedRect.xCenter - reconvertedNormalized.xCenter),
      yCenter: Math.abs(normalizedRect.yCenter - reconvertedNormalized.yCenter),
      width: Math.abs(normalizedRect.width - reconvertedNormalized.width),
      height: Math.abs(normalizedRect.height - reconvertedNormalized.height)
    };
    
    const maxError = Math.max(conversionError.xCenter, conversionError.yCenter, conversionError.width, conversionError.height);
    const isConversionConsistent = maxError < 0.001; // Allow small floating point errors
    
    console.log('🧪 DETECTION_TO_RECT: Conversion consistency test:', {
      original: normalizedRect,
      reconverted: reconvertedNormalized,
      error: conversionError,
      maxError,
      consistent: isConversionConsistent
    });
    
    if (!isConversionConsistent) {
      console.error('🧪 DETECTION_TO_RECT: Coordinate conversion consistency failed!');
    }

    // Test boundary conditions
    testBoundaryConditions(detection, imageSize);

  } catch (error) {
    console.error('🧪 DETECTION_TO_RECT: Error during coordinate conversion testing:', error);
  }
}

function testBoundaryConditions(detection: Detection, imageSize: ImageSize) {
  console.log('🧪 DETECTION_TO_RECT: Testing boundary conditions');
  
  // Create test cases for boundary conditions
  const testCases = [
    // Edge of image
    { xMin: 0, yMin: 0, width: 0.1, height: 0.1, xMax: 0.1, yMax: 0.1 },
    // Center of image  
    { xMin: 0.45, yMin: 0.45, width: 0.1, height: 0.1, xMax: 0.55, yMax: 0.55 },
    // Near right edge
    { xMin: 0.9, yMin: 0.45, width: 0.1, height: 0.1, xMax: 1.0, yMax: 0.55 },
    // Near bottom edge
    { xMin: 0.45, yMin: 0.9, width: 0.1, height: 0.1, xMax: 0.55, yMax: 1.0 },
    // Very small box
    { xMin: 0.5, yMin: 0.5, width: 0.01, height: 0.01, xMax: 0.51, yMax: 0.51 },
    // Large box
    { xMin: 0.1, yMin: 0.1, width: 0.8, height: 0.8, xMax: 0.9, yMax: 0.9 }
  ];

  testCases.forEach((testBox, index) => {
    console.log(`🧪 DETECTION_TO_RECT: Testing boundary case ${index + 1}:`, testBox);
    
    try {
      const normalizedRect = rectFromBox(testBox, true);
      const pixelVersion = {
        xMin: testBox.xMin * imageSize.width,
        yMin: testBox.yMin * imageSize.height,
        width: testBox.width * imageSize.width,
        height: testBox.height * imageSize.height,
        xMax: testBox.xMax * imageSize.width,
        yMax: testBox.yMax * imageSize.height
      };
      const pixelRect = rectFromBox(pixelVersion, false);
      
      console.log(`🧪 DETECTION_TO_RECT: Boundary case ${index + 1} results:`, {
        normalized: normalizedRect,
        pixel: pixelRect
      });
      
      // Validate results are reasonable
      if (normalizedRect.xCenter < 0 || normalizedRect.xCenter > 1 ||
          normalizedRect.yCenter < 0 || normalizedRect.yCenter > 1) {
        console.error(`🧪 DETECTION_TO_RECT: Boundary case ${index + 1} failed - normalized coordinates out of bounds`);
      }
      
      if (pixelRect.xCenter < 0 || pixelRect.yCenter < 0 ||
          pixelRect.width <= 0 || pixelRect.height <= 0) {
        console.error(`🧪 DETECTION_TO_RECT: Boundary case ${index + 1} failed - invalid pixel coordinates`);
      }
      
    } catch (error) {
      console.error(`🧪 DETECTION_TO_RECT: Boundary case ${index + 1} threw error:`, error);
    }
  });
}

export function calculateDetectionsToRects(
    detection: Detection, conversionMode: 'boundingbox'|'keypoints',
    outputType: 'rect'|'normRect', imageSize?: ImageSize,
    rotationConfig?: DetectionToRectConfig): Rect {
  
  console.log('🔧 DETECTION_TO_RECT: calculateDetectionsToRects called');
  console.log('🔧 DETECTION_TO_RECT: Parameters - conversionMode:', conversionMode, 'outputType:', outputType);
  console.log('🔧 DETECTION_TO_RECT: ImageSize:', imageSize);
  console.log('🔧 DETECTION_TO_RECT: Detection:', detection);

  // FIXED: Add comprehensive validation
  if (!detection) {
    console.error('🔧 DETECTION_TO_RECT: No detection provided');
    return {
      xCenter: 0.5,
      yCenter: 0.5,
      width: 0.1,
      height: 0.1,
    };
  }

  try {
    const rect: Rect = outputType === 'rect' ?
        detectionToRect(detection, conversionMode, imageSize) :
        detectionToNormalizedRect(detection, conversionMode);

    // TASK 4: Run coordinate conversion tests if imageSize is available
    if (imageSize) {
      testCoordinateConversions(detection, imageSize);
    }

    // TASK 6: Run edge case validation tests periodically  
    if (Math.random() < 0.1) { // Run edge case tests 10% of the time to avoid performance impact
      validateRectEdgeCases();
    }

    // TASK 6: Run performance validation occasionally
    if (Math.random() < 0.05) { // Run performance tests 5% of the time
      validateConversionPerformance(detection, 50); // Reduced iterations for production
    }

    if (rotationConfig && imageSize) {
      console.log('🔧 DETECTION_TO_RECT: Computing rotation with config');
      rect.rotation = computeRotation(detection, imageSize, rotationConfig);
    } else if (rotationConfig) {
      console.warn('🔧 DETECTION_TO_RECT: Rotation config provided but no imageSize - skipping rotation');
    }

    console.log('🔧 DETECTION_TO_RECT: Final output rect:', rect);
    return rect;

  } catch (error) {
    console.error('🔧 DETECTION_TO_RECT: Error in calculateDetectionsToRects:', error);
    // Return safe fallback
    return {
      xCenter: outputType === 'rect' && imageSize ? imageSize.width / 2 : 0.5,
      yCenter: outputType === 'rect' && imageSize ? imageSize.height / 2 : 0.5,
      width: outputType === 'rect' && imageSize ? imageSize.width * 0.1 : 0.1,
      height: outputType === 'rect' && imageSize ? imageSize.height * 0.1 : 0.1,
    };
  }
}
