
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';

/**
 * Converts normalized keypoints (0-1 range) to actual pixel coordinates.
 * This is essential for displaying landmarks correctly on the image.
 */
export function normalizedKeypointsToKeypoints(
    normalizedKeypoints: Keypoint[],
    imageSize: ImageSize): Keypoint[] {
  
  console.log('🔧 COORDINATE SCALING: Converting normalized to pixel coordinates');
  console.log('🔧 COORDINATE SCALING: Image size:', imageSize);
  console.log('🔧 COORDINATE SCALING: Input keypoints count:', normalizedKeypoints.length);

  const scaledKeypoints: Keypoint[] = normalizedKeypoints.map((keypoint, index) => {
    // PHASE 6B: NaN-safe coordinate scaling
    let normalizedX = keypoint.x;
    let normalizedY = keypoint.y;
    let score = keypoint.score;
    
    // PHASE 6B: Validate and fix NaN values before scaling
    if (isNaN(normalizedX) || !isFinite(normalizedX)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN normalized X at keypoint ${index}`);
      normalizedX = 0.5;
    }
    if (isNaN(normalizedY) || !isFinite(normalizedY)) {
      console.warn(`🔧 PHASE 6B: Fixed NaN normalized Y at keypoint ${index}`);
      normalizedY = 0.5;
    }
    if (score !== undefined && (isNaN(score) || !isFinite(score))) {
      console.warn(`🔧 PHASE 6B: Fixed NaN score at keypoint ${index}`);
      score = 0.5;
    }
    
    // PHASE 6B: Safe scaling with bounds checking
    let scaledX = normalizedX * imageSize.width;
    let scaledY = normalizedY * imageSize.height;
    
    // PHASE 6B: Ensure scaled coordinates are within reasonable bounds
    if (isNaN(scaledX) || !isFinite(scaledX) || scaledX < 0 || scaledX > imageSize.width * 2) {
      console.warn(`🔧 PHASE 6B: Clamped scaled X coordinate at keypoint ${index}`);
      scaledX = imageSize.width * 0.5;
    }
    if (isNaN(scaledY) || !isFinite(scaledY) || scaledY < 0 || scaledY > imageSize.height * 2) {
      console.warn(`🔧 PHASE 6B: Clamped scaled Y coordinate at keypoint ${index}`);
      scaledY = imageSize.height * 0.5;
    }
    
    const scaledKeypoint: Keypoint = {
      x: scaledX,
      y: scaledY,
      score: score,
      name: keypoint.name
    };

    // Preserve z-coordinate if it exists (for 3D landmarks)
    if (keypoint.z !== undefined) {
      let scaledZ = keypoint.z;
      if (isNaN(scaledZ) || !isFinite(scaledZ)) {
        scaledZ = 0;
      }
      scaledKeypoint.z = scaledZ;
    }

    // Log first few keypoints for debugging
    if (index < 5) {
      console.log(`🔧 PHASE 6B: Keypoint ${index} NaN-safe scaling:`, {
        normalized: { x: normalizedX.toFixed(4), y: normalizedY.toFixed(4) },
        scaled: { x: scaledX.toFixed(1), y: scaledY.toFixed(1) },
        score: score?.toFixed(4),
        imageSize: { w: imageSize.width, h: imageSize.height }
      });
    }

    return scaledKeypoint;
  });

  console.log(`🔧 COORDINATE SCALING: Successfully scaled ${scaledKeypoints.length} keypoints`);
  return scaledKeypoints;
}
