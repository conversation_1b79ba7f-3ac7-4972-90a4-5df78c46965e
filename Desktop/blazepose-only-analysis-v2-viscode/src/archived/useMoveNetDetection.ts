
import { useState, useEffect } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as poseDetection from '@tensorflow-models/pose-detection';

export const useMoveNetDetection = () => {
  const [detector, setDetector] = useState<poseDetection.PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    const initMoveNet = async () => {
      console.log('🔄 SEALED 2D PIPELINE: Initializing MoveNet ONLY...');
      try {
        await tf.ready();
        console.log('✅ TensorFlow.js ready for MoveNet (2D)');
        
        const model = poseDetection.SupportedModels.MoveNet;
        const detectorConfig = {
          modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
        };
        
        const moveNetDetector = await poseDetection.createDetector(model, detectorConfig);
        setDetector(moveNetDetector);
        setIsInitialized(true);
        console.log('✅ SEALED 2D PIPELINE: MoveNet detector initialized successfully');
        console.log('❌ SEALED 2D PIPELINE: NO BlazePose allowed in this hook');
        
        setDebugInfo('2D Pipeline: MoveNet ONLY - Initialized');
      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: MoveNet initialization failed:', error);
        setDebugInfo(`2D MoveNet Init Error: ${error.message}`);
      }
    };

    initMoveNet();
  }, []);

  const detectPoses = async (video: HTMLVideoElement) => {
    console.log('🔍 SEALED 2D PIPELINE: Running MoveNet pose detection ONLY');
    
    if (!detector || !video) {
      console.log('❌ SEALED 2D PIPELINE: Missing MoveNet detector or video');
      return [];
    }

    try {
      console.log('🚀 SEALED 2D PIPELINE: MoveNet estimating poses...');
      const poses = await detector.estimatePoses(video);
      console.log(`✅ SEALED 2D PIPELINE: MoveNet detected ${poses.length} poses`);
      return poses;
    } catch (error) {
      console.error('❌ SEALED 2D PIPELINE: MoveNet detection error:', error);
      setDebugInfo(`2D MoveNet Error: ${error.message}`);
      return [];
    }
  };

  return {
    detector,
    isInitialized,
    debugInfo,
    setDebugInfo,
    detectPoses
  };
};
