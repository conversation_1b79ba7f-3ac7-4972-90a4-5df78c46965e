
import React, { useRef, useEffect, useState } from 'react';
import { useMoveNetDetection } from '@/archived/useMoveNetDetection';
import { 
  scaleKeypoints, 
  drawPoseConnections, 
  drawPoseKeypoints, 
  drawDebugFrame 
} from '@/utils/poseRenderer';
import { processPoseData } from '@/utils/poseCalculations';

interface RunningPoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
  overlayStyle?: string;
  userHeight?: { feet: number; inches: number };
}

const RunningPoseOverlay: React.FC<RunningPoseOverlayProps> = ({ 
  videoRef, 
  onPoseData,
  overlayStyle = 'Medical',
  userHeight = { feet: 5, inches: 10 }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const { detector, isInitialized, debugInfo, setDebugInfo, detectPoses } = useMoveNetDetection();
  const [frameCount, setFrameCount] = useState(0);

  // Convert user height to meters for calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    console.log('🔍 RUNNING SEALED PIPELINE AUDIT:');
    console.log('✅ SEALED 2D PIPELINE: This component uses MoveNet ONLY');
    console.log('❌ SEALED 2D PIPELINE: NO BlazePose allowed here');
    console.log('🎨 Overlay Style:', overlayStyle);
    console.log('👤 User height:', `${userHeight.feet}'${userHeight.inches}"`, `(${userHeightMeters.toFixed(2)}m)`);
    
    if (animationFrameRef.current) {
      console.log('🛑 Canceling previous animation frame');
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      console.log('⏸️ SEALED 2D PIPELINE: MoveNet not ready yet');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      console.error('❌ Failed to get 2D context from canvas');
      return;
    }

    console.log('✅ SEALED 2D PIPELINE: Starting running pose detection - NO BlazePose');

    const detectPose = async () => {
      try {
        if (!video || video.paused || video.ended) {
          return;
        }

        setFrameCount(prev => prev + 1);
        const currentFrame = frameCount + 1;
        
        if (currentFrame % 30 === 0) {
          console.log(`🏃 SEALED 2D PIPELINE: Running Frame ${currentFrame}`);
        }

        // Wait for video to be ready
        if (video.videoWidth === 0 || video.videoHeight === 0 || video.readyState < 2) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        const rect = video.getBoundingClientRect();
        
        if (rect.width === 0 || rect.height === 0) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Set canvas size and position
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw debug frame
        drawDebugFrame(ctx, currentFrame);
        
        // SEALED 2D PIPELINE: Use MoveNet detector ONLY
        const poses = await detectPoses(video);
        
        if (poses.length > 0 && poses[0].keypoints) {
          const convertedKeypoints = poses[0].keypoints.map((kp: any) => ({
            x: kp.x || 0,
            y: kp.y || 0,
            score: kp.score ?? 0
          }));
          
          // Scale keypoints with user height parameter
          const scaledKeypoints = scaleKeypoints(
            convertedKeypoints,
            rect.width,
            rect.height,
            video.videoWidth,
            video.videoHeight,
            userHeightMeters
          );
          
          // Draw pose based on overlay style
          if (overlayStyle === 'Medical') {
            drawPoseConnections(ctx, scaledKeypoints);
            drawPoseKeypoints(ctx, scaledKeypoints);
          } else {
            drawPoseConnections(ctx, scaledKeypoints);
            drawPoseKeypoints(ctx, scaledKeypoints);
          }
          
          // Process pose data
          try {
            const poseData = processPoseData(poses, currentFrame);
            if (poseData && onPoseData) {
              onPoseData({
                ...poseData,
                pipeline: '2D-MoveNet-ONLY',
                userHeight: userHeight
              });
            }
          } catch (processError) {
            console.error('❌ SEALED 2D PIPELINE: Processing error:', processError);
          }
          
          setDebugInfo(`✓ SEALED 2D PIPELINE Frame ${currentFrame}: ${poses[0].keypoints.length} keypoints - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        } else {
          setDebugInfo(`SEALED 2D PIPELINE Frame ${currentFrame}: No poses - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        }
        
      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: Pose detection error:', error);
        setDebugInfo(`2D Error: ${error.message}`);
      }

      // Continue animation loop if video is still playing
      if (video && !video.paused && !video.ended) {
        animationFrameRef.current = requestAnimationFrame(detectPose);
      }
    };

    // Start detection with a small delay
    const startTimeout = setTimeout(() => {
      detectPose();
    }, 100);

    return () => {
      console.log('🛑 SEALED 2D PIPELINE: Cleaning up running pose detection');
      clearTimeout(startTimeout);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isInitialized, videoRef, detectPoses, setDebugInfo, onPoseData, overlayStyle, userHeight, userHeightMeters, frameCount]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    />
  );
};

export default RunningPoseOverlay;
