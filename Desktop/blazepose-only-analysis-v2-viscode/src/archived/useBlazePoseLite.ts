
import { useState, useEffect } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as poseDetection from '@tensorflow-models/pose-detection';
import { tensorsToLandmarks, tensorsToWorldLandmarks } from '@/shared/calculators/tensors_to_landmarks';
import { BLAZEPOSE_LITE_CONFIG, BLAZEPOSE_TENSORS_TO_LANDMARKS_CONFIG, BLAZEPOSE_KEYPOINTS } from '@/shared/calculators/blazepose_constants';

export const useBlazePoseLite = () => {
  const [detector, setDetector] = useState<poseDetection.PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    const initBlazePoseLite = async () => {
      console.log('🔄 Initializing BlazePose Lite with enhanced tensor processing...');
      try {
        setError(null);
        
        // Ensure TensorFlow is ready
        await tf.ready();
        console.log('✅ TensorFlow.js ready');
        
        // Try WebGL first, fallback to CPU
        try {
          await tf.setBackend('webgl');
          await tf.ready();
          console.log('✅ WebGL backend ready');
        } catch (webglError) {
          console.warn('⚠️ WebGL failed, using CPU backend:', webglError);
          await tf.setBackend('cpu');
          await tf.ready();
        }

        // Configure BlazePose Lite with proper settings
        const blazePoseConfig: poseDetection.BlazePoseTfjsModelConfig = {
          runtime: 'tfjs' as const,
          modelType: 'lite' as const,
          enableSmoothing: false,
          enableSegmentation: false,
          smoothSegmentation: false
        };
        
        console.log('🔧 BlazePose Lite config:', blazePoseConfig);
        
        // Create detector
        const blazePoseDetector = await poseDetection.createDetector(
          poseDetection.SupportedModels.BlazePose, 
          blazePoseConfig
        );
        
        if (!blazePoseDetector) {
          throw new Error('BlazePose Lite detector creation failed');
        }
        
        setDetector(blazePoseDetector);
        setIsInitialized(true);
        setDebugInfo(`BlazePose Lite initialized - Backend: ${tf.getBackend()}`);
        console.log('✅ BlazePose Lite detector ready with enhanced processing');
        
      } catch (error) {
        console.error('❌ BlazePose Lite initialization failed:', error);
        setError(`BlazePose Lite Error: ${error.message}`);
        setDebugInfo(`BlazePose Lite Error: ${error.message}`);
        setIsInitialized(false);
      }
    };

    initBlazePoseLite();
    
    return () => {
      if (detector) {
        console.log('🧹 Cleaning up BlazePose Lite detector');
        try {
          detector.dispose();
        } catch (disposeError) {
          console.warn('⚠️ Error disposing detector:', disposeError);
        }
      }
    };
  }, []);

  const detectPoses = async (video: HTMLVideoElement) => {
    if (!detector || !video || !isInitialized) {
      console.log('❌ Detector not ready - detector:', !!detector, 'video:', !!video, 'initialized:', isInitialized);
      return [];
    }

    // Enhanced video readiness check
    if (video.readyState < 3) {
      console.log('❌ Video not ready - readyState:', video.readyState);
      return [];
    }

    if (video.videoWidth === 0 || video.videoHeight === 0) {
      console.log('❌ Video dimensions invalid - width:', video.videoWidth, 'height:', video.videoHeight);
      return [];
    }

    if (video.paused || video.ended) {
      console.log('❌ Video not playing - paused:', video.paused, 'ended:', video.ended);
      return [];
    }

    try {
      console.log('🔍 BlazePose Lite detecting poses with enhanced processing...');
      
      const estimationConfig: poseDetection.BlazePoseTfjsEstimationConfig = {
        maxPoses: 1,
        flipHorizontal: false
      };
      
      // Get raw model predictions first
      const poses = await detector.estimatePoses(video, estimationConfig);
      
      console.log(`✅ BlazePose Lite raw detection: ${poses.length} poses`);
      
      if (poses.length > 0 && poses[0].keypoints) {
        const pose = poses[0];
        
        // Enhanced coordinate processing with proper validation
        const processedPoses = await processBlazePoseOutput(pose, video);
        
        console.log('🎯 Enhanced pose processing complete:', {
          originalKeypoints: pose.keypoints.length,
          processedKeypoints: processedPoses[0]?.keypoints?.length || 0,
          has3D: !!processedPoses[0]?.keypoints3D,
          keypoints3D: processedPoses[0]?.keypoints3D?.length || 0
        });
        
        // Log sample coordinates for verification
        if (processedPoses[0]?.keypoints?.length > 0) {
          const sample = processedPoses[0].keypoints[0];
          console.log('📍 Sample ENHANCED keypoint:', {
            name: sample.name,
            x: sample.x?.toFixed(3),
            y: sample.y?.toFixed(3),
            z: sample.z?.toFixed(3),
            score: sample.score?.toFixed(3),
            isValidX: !isNaN(sample.x),
            isValidY: !isNaN(sample.y)
          });
        }
        
        return processedPoses;
      }
      
      return poses;
      
    } catch (error) {
      console.error('❌ BlazePose Lite detection error:', error);
      setError(`Detection error: ${error.message}`);
      return [];
    }
  };

  /**
   * Enhanced BlazePose output processing with proper tensor handling
   */
  const processBlazePoseOutput = async (pose: any, video: HTMLVideoElement) => {
    console.log('🔧 ENHANCED PROCESSING: Processing BlazePose output');
    
    try {
      // Process keypoints with enhanced validation
      const enhancedKeypoints = pose.keypoints.map((kp: any, index: number) => {
        // Get keypoint name from BlazePose keypoint list
        const keypointName = BLAZEPOSE_KEYPOINTS[index] || `landmark_${index}`;
        
        let x = kp.x;
        let y = kp.y;
        let z = kp.z || 0;
        let score = kp.score ?? 0;

        // Enhanced coordinate validation
        if (isNaN(x) || x === undefined || x === null) {
          console.warn(`🔧 ENHANCED: Fixed NaN x coordinate for ${keypointName}`);
          x = video.videoWidth * 0.5; // Center default
        }
        if (isNaN(y) || y === undefined || y === null) {
          console.warn(`🔧 ENHANCED: Fixed NaN y coordinate for ${keypointName}`);
          y = video.videoHeight * 0.5; // Center default
        }
        if (isNaN(z) || z === undefined || z === null) {
          z = 0;
        }
        if (isNaN(score) || score === undefined || score === null) {
          score = 0.1; // Low confidence default
        }

        // Normalize coordinates to 0-1 range
        const normalizedX = Math.max(0, Math.min(1, x / video.videoWidth));
        const normalizedY = Math.max(0, Math.min(1, y / video.videoHeight));

        return {
          x: normalizedX,
          y: normalizedY,
          z: z,
          score: Math.max(0, Math.min(1, score)),
          name: keypointName
        };
      });

      // Process 3D keypoints if available
      let enhanced3DKeypoints = null;
      if (pose.keypoints3D) {
        enhanced3DKeypoints = pose.keypoints3D.map((kp: any, index: number) => {
          const keypointName = BLAZEPOSE_KEYPOINTS[index] || `world_landmark_${index}`;
          
          let x = kp.x || 0;
          let y = kp.y || 0;
          let z = kp.z || 0;
          let score = kp.score ?? 0;

          // Fix NaN values for 3D coordinates
          if (isNaN(x)) x = 0;
          if (isNaN(y)) y = 0;
          if (isNaN(z)) z = 0;
          if (isNaN(score)) score = 0.1;

          return { 
            x, 
            y, 
            z, 
            score: Math.max(0, Math.min(1, score)), 
            name: keypointName 
          };
        });
      }

      const enhancedPose = {
        ...pose,
        keypoints: enhancedKeypoints,
        keypoints3D: enhanced3DKeypoints,
        score: pose.score || 0.5
      };

      console.log('✅ ENHANCED PROCESSING: Completed pose enhancement');
      console.log('📊 ENHANCED STATS:', {
        keypoints2D: enhancedKeypoints.length,
        keypoints3D: enhanced3DKeypoints?.length || 0,
        averageScore: enhancedKeypoints.reduce((sum, kp) => sum + kp.score, 0) / enhancedKeypoints.length
      });

      return [enhancedPose];
      
    } catch (error) {
      console.error('❌ ENHANCED PROCESSING: Error processing pose output:', error);
      return [pose]; // Return original pose as fallback
    }
  };

  return {
    detector,
    isInitialized,
    error,
    debugInfo,
    setDebugInfo,
    detectPoses
  };
};
