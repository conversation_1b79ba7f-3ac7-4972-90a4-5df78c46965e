/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */

export * from './blazepose_tfjs/types';
export * from './blazepose_mediapipe/types';
export * from './pose_detector';
export * from './shared/calculators/blazepose_constants';
export * from './shared/calculators/interfaces/common_interfaces';

export enum SupportedModels {
  BlazePose = 'BlazePose',
  MoveNet = 'MoveNet',
  PoseNet = 'PoseNet'
}

export async function createDetector(
  model: SupportedModels,
  modelConfig?: any
): Promise<any> {
  // Implementation will be added later
  throw new Error('createDetector not implemented yet');
}

export const util = {
  getAdjacentPairs: (model: SupportedModels) => {
    // Implementation for getting adjacent pairs
    return [];
  }
};