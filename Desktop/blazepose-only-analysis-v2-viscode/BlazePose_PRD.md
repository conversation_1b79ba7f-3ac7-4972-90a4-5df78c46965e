# BlazePose Skeletal Overlay - Product Requirements Document

## Project Goal
Achieve consistent skeletal overlay visualization during video playback with minimal jitter, similar to the reference image provided. The system should display real-time pose detection with skeleton connections overlaid on the running video.

## Current Issues Analysis

### 1. Detection Pipeline Failures
**Issue**: `TypeError: Cannot read properties of undefined (reading 'relativeBoundingBox')`
**Impact**: Complete pipeline breakdown, no pose detection
**Root Cause**: Data structure mismatch between detector output and expected input format

### 2. Inconsistent Pose Detection  
**Issue**: Sporadic detection success (some frames work, others fail)
**Impact**: Jittery/unstable skeletal overlay
**Root Cause**: Configuration mismatches and filtering threshold issues

### 3. Missing Skeletal Visualization
**Issue**: No visible skeleton overlay on video despite detection attempts
**Impact**: No user-visible output
**Root Cause**: Canvas positioning, coordinate system misalignment, or drawing logic issues

### 4. Model Configuration Problems
**Issue**: Lite model limitations with side-view facial detection
**Impact**: Reduced detection reliability
**Root Cause**: Model choice inadequate for use case

### Root Cause Analysis
- **Primary**: Tensor processing pipeline breaks at detection-to-rect conversion
- **Secondary**: Configuration mismatches between different pipeline stages
- **Tertiary**: Coordinate system transformations not properly aligned

## Model Strategy Change
**Decision**: Switch from BlazePose Lite to BlazePose Full for side-view analysis
**Rationale**: 
- Full model more robust with partial facial visibility
- Better consistency for side-view scenarios
- Lite model optimization can be addressed later for rear-view

## Files Analysis & Task Breakdown

### 🔴 CRITICAL - Core Pipeline Files

#### `src/shared/calculators/tensors_to_detections.ts`
**Risk Level**: CRITICAL - Pipeline likely breaks here
**Current Issues**:
- Converts raw detector tensors to detection objects
- Tensor indexing potentially incorrect
- BlazePose tensor format expectations may be wrong

**Tasks**:
1. Verify tensor shape expectations (should be [1, numBoxes, 13] for BlazePose)
2. Fix coordinate system interpretation (BlazePose: yCenter, xCenter, height, width)
3. Validate anchor-based coordinate transformation
4. Test tensor data extraction and indexing
5. Add comprehensive logging for tensor debugging

#### `src/shared/calculators/detector_result.ts`
**Risk Level**: CRITICAL - Modified but needs verification
**Current Issues**:
- Tensor splitting logic may be incorrect
- BlazePose format assumptions need validation

**Tasks**:
1. Verify BlazePose tensor format: [batch, anchors, 13] where 13 = [4 box coords, 1 score, 8 additional]
2. Confirm correct slicing for boxes (indices 0-3) and scores (index 4)
3. Validate tensor reshaping operations
4. Test with actual BlazePose Full model output
5. Add tensor shape validation

#### `src/shared/calculators/detection_to_rect.ts`
**Risk Level**: CRITICAL - Source of relativeBoundingBox error
**Current Issues**:
- `relativeBoundingBox` property access failing
- Coordinate system conversion problems
- Validation logic may be too strict

**Tasks**:
1. Fix relativeBoundingBox/boundingBox property access logic
2. Add null/undefined checks for all detection properties
3. Verify coordinate normalization (0-1 range vs pixel coordinates)
4. Test both normalized and absolute coordinate paths
5. Add fallback mechanisms for missing properties
6. Validate rect conversion logic

### 🟡 HIGH PRIORITY - Configuration Files

#### `src/blazepose_tfjs/constants.ts`
**Risk Level**: HIGH - Configuration mismatches causing subtle bugs
**Current Issues**:
- Tensor dimensions may not align with Full model
- Detection thresholds potentially incorrect

**Tasks**:
1. Update configuration for BlazePose Full model (vs Lite)
2. Verify tensor dimensions match model output
3. Adjust detection confidence thresholds
4. Confirm input size requirements (256x256 vs other sizes)
5. Validate anchor configuration

#### `src/blazepose_tfjs/detector.ts`
**Risk Level**: HIGH - Main orchestration logic
**Current Issues**:
- Model loading configuration may be incorrect
- Pipeline coordination between phases

**Tasks**:
1. Update model loading for Full instead of Lite
2. Verify pipeline phase coordination
3. Add error handling for each pipeline stage
4. Implement proper tensor cleanup
5. Add debugging instrumentation

### 🟡 MEDIUM PRIORITY - Filtering & Processing

#### `src/shared/calculators/non_max_suppression.ts`
**Risk Level**: MEDIUM - May be filtering out all valid detections
**Current Issues**:
- Thresholds too aggressive (0.5 → 0.1 mentioned)
- Could be removing all valid detections

**Tasks**:
1. Review and adjust confidence thresholds
2. Test with more permissive filtering initially
3. Validate IoU calculations
4. Add logging to track filtered vs retained detections

#### `src/hooks/useBlazePoseDetection.ts`
**Risk Level**: MEDIUM - Model loading and processing coordination
**Current Issues**:
- Hardcoded to Lite model
- Processing pipeline may have issues

**Tasks**:
1. Change model initialization from 'Lite' to 'Full'
2. Update model configuration parameters
3. Verify processing pipeline compatibility
4. Add model-specific error handling
5. Update debug information display

### 🟢 LOW PRIORITY - Visualization & UI

#### `src/components/SideViewBlazePoseOverlay.tsx`
**Risk Level**: LOW - Visualization layer
**Current Issues**:
- Canvas positioning and sizing
- Coordinate system for drawing
- Animation frame management

**Tasks**:
1. Verify canvas overlay positioning matches video
2. Fix coordinate scaling for skeleton drawing
3. Improve error handling and circuit breaker logic
4. Optimize animation frame performance
5. Add visual debugging indicators

#### `src/utils/blazePoseRenderer.ts`
**Risk Level**: LOW - Rendering utilities
**Tasks**:
1. Verify skeleton connection definitions
2. Update rendering for Full model keypoints
3. Improve visual styling and colors
4. Add keypoint confidence visualization

### 🔧 SUPPORTING FILES

#### `src/shared/calculators/blazepose_constants.ts`
**Risk Level**: LOW - Constants and definitions
**Tasks**:
1. Verify keypoint definitions match Full model
2. Update connection definitions if needed
3. Add Full model specific constants

## Additional Considerations

### Configuration Alignment Issues
- **Input Size Mismatch**: Ensure all pipeline stages expect same input dimensions
- **Coordinate Systems**: Normalize all coordinate transformations
- **Tensor Formats**: Verify tensor shape expectations across all calculators

### Performance Optimizations
- Implement proper tensor disposal to prevent memory leaks
- Optimize animation frame rendering
- Add performance monitoring

### Error Handling & Debugging
- Add comprehensive logging at each pipeline stage
- Implement graceful fallbacks for processing failures
- Create debugging visualization tools

### Testing Strategy
- Unit tests for individual calculator functions
- Integration tests for full pipeline
- Visual regression tests for skeleton overlay accuracy

## Success Criteria
1. **Consistent Detection**: >90% frame detection success rate
2. **Stable Visualization**: Skeleton overlay visible and stable during playback
3. **Performance**: <100ms processing time per frame
4. **Accuracy**: Skeleton joints align with actual body positions
5. **Reliability**: No pipeline crashes or undefined errors

## Phase 1 Priority Order
1. Fix `tensors_to_detections.ts` - Enable basic detection
2. Fix `detection_to_rect.ts` - Resolve relativeBoundingBox error
3. Update model to Full in `useBlazePoseDetection.ts`
4. Verify configuration alignment in `constants.ts`
5. Test and debug visualization pipeline

## Questions for Clarification
1. Should we maintain backward compatibility with Lite model for future rear-view work?
2. Are there specific performance requirements for real-time processing?
3. Do you need any specific analytics or metrics collection during processing?
4. Should we implement any specific error recovery mechanisms?